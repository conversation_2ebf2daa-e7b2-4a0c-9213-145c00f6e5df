package com.yy.gameecology.activity;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.bean.UpdateTaskReq;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2023/7/17 14:17
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, properties = {"group=1"})
@TestPropertySource(value = {
        "classpath:env/local/application.properties",
        "classpath:env/local/application-inner.properties",
        "classpath:env/local/group-setting-1.properties"
})
public class LuaTest extends BaseTest {
    static {
        System.setProperty("group", "1");
    }

    @Autowired
    private ActRedisGroupDao actRedisGroupDao;

    @Test
    public void zIncrWithSeqTest() {
        String groupCode = "1";
        String zKey = "test_zincr";
        String memberId = "krista001";
        long score = 100;
        String seq = "test_zincr:" + UUID.randomUUID();

        Object totalValue = actRedisGroupDao.zIncrWithSeq(groupCode, seq, zKey, memberId, score);
        System.out.println(totalValue);
        totalValue = actRedisGroupDao.zIncrWithSeq(groupCode, seq, zKey, memberId, score);
        System.out.println(totalValue);
        seq = "test_zincr:" + UUID.randomUUID();
        totalValue = actRedisGroupDao.zIncrWithSeq(groupCode, seq, zKey, memberId, score);
        System.out.println(totalValue);
    }

    @Test
    public void hIncrByKeyWithSeqTest() {
        Long totalValue = actRedisGroupDao.hIncrByKeyWithSeq("1", "2222", "krista2", "w", 100, 0);
        System.out.println(totalValue);
    }

    @Test
    public void updateTaskTest() {
        UpdateTaskReq updateTaskReq = new UpdateTaskReq();
        updateTaskReq.setKeyPrefix("update_task_test");
        updateTaskReq.setSeqKeyPrefix("update_task_test:seq");
        updateTaskReq.setSeq(System.currentTimeMillis() + "");
        updateTaskReq.setRepeatedCheckExpire(3600);
        updateTaskReq.setSaveCurResult(1);
        updateTaskReq.setScore(150);
        updateTaskReq.setMember("zengwenzhi");
        updateTaskReq.setRecycleCount(-1);
        updateTaskReq.setContributeMember(ImmutableMap.of("200", "zhangsan"));
        updateTaskReq.setTaskConfig(Lists.newArrayList(300L));
        updateTaskReq.setLogCntlFlag("1");
        actRedisGroupDao.updateTask("1", updateTaskReq);

    }
}
