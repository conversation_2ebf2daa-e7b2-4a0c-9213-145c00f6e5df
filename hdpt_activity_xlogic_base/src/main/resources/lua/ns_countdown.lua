---------------------------------------------------------------------------------
-- 通用排队倒计时，用于排队开宝箱等玩法  - guoliping/2021-11-13
-- 核心逻辑：
--  1）list元素结构为：${倒计时开始时刻}|${排队条目ID}，${倒计时开始时刻}初始为0，开始后为 yyyyMMddHHmmss（为便于维护不用秒数）
--  2）先验证现场是否被破坏（队首元素 和 将要处理的倒计时条目是否H相等）
--  3）若 队首元素的头部为 0，则初始化启动数据
--  4）已启动的倒计时为0以下则移除倒计时条目
---------------------------------------------------------------------------------

-- 条目列表KEY， list 结构
local listKey = KEYS[1]

-- 标记条目倒计时开始的 string key（一般用于控制用户报名等，若这个key不存在，就不能报名）， 配合 expiration 工作
local nsCountdownKey = KEYS[2]
--已扣除的血量key
local bloodKey = KEYS[3]
--频道年兽数量
local nsCount = KEYS[4]

local dismissKey = KEYS[5]
-- 被打死时刻
local deadTimeKey = KEYS[6]

-- 倒计时剩余的秒数, 含结果展示时间, preTime + fightTime + showResultTime
local left = tonumber(ARGV[1])

-- 宝箱ID
local nsId = ARGV[2]

-- 宝箱到时时的开始时刻，数据格式： 0 or yyyyMMddHHmmss
local startTime = tonumber(ARGV[3])

-- 准备阶段key
local preTime = tonumber(ARGV[4])

-- 战斗时间
local fightTime = tonumber(ARGV[5])

-- 展示时间
local showResultTime = tonumber(ARGV[6])

-- sid_ssid
local sidSsid = ARGV[7]

local totalBlood = tonumber(ARGV[8])

--当前时间, 单位:秒
local nowTime = tonumber(ARGV[9])

-- 判断字符串是否为空
local function empty(s)
    return s == nil or s == ''
end

-- 1. 验证现场：若首元素 != 老元素，说明现场已经破坏， 直接返回
local oldItem = startTime .. '|' .. nsId .. '|' .. totalBlood
local currItem = redis.call('LINDEX', listKey, 0)
if currItem ~= oldItem then
    return -1
end

-- 2. 若宝箱倒计时还没开始，初始化宝箱倒计时数据,
if startTime == 0 then
    if not empty(nsCountdownKey) then
        -- 倒计时标记key已经存在，失败返回
        if redis.call('SETNX', nsCountdownKey, nowTime) == 0 then
            return -2
        end

        -- 设置倒计时标记key的过期时间
        --if expireSeconds > 0 then
        redis.call('EXPIRE', nsCountdownKey, preTime + fightTime)
        -- end
    end

    -- 更新首元素，设置倒计时标记key的过期时间，这个操作必须成功，否则数据可能不一致
    local newItem = nowTime .. '|' .. nsId .. '|' .. totalBlood
    redis.call('LSET', listKey, 0, newItem)

    -- 更新正在打年兽频道信息  准备时间 + 战斗时间
    redis.call("HSET", dismissKey, sidSsid, (nowTime + preTime + fightTime) .. '|' .. nsId)
end

-- 3. 若血量已扣完,则弹出, 计算减 1 若倒计时结束了，需要去掉首元素
local blood = tonumber(redis.call("hget", bloodKey, nsId)) or 0

local deadTime = tonumber(redis.call("HGET", deadTimeKey, nsId)) or 0

-- 删除已过期的, 或者死亡时间大于展示时间
if left <= 0 or (deadTime > 0 and nowTime - deadTime >= showResultTime) then
    redis.call('LPOP', listKey)
    local count = tonumber(redis.call('hincrby', nsCount, sidSsid, -1)) or 0
    if count == 0 then
        redis.call('hdel', nsCount, sidSsid)
    end
end

-- todo 处理提前结束 done
if blood >= totalBlood then
    --记录被打败的时刻
    redis.call("HSETNX", deadTimeKey, nsId, nowTime)
    -- 当前频道未在打年兽,删除key
    redis.call("HDEL", dismissKey, sidSsid)
    return 2
end

-- 4. 未打死年兽,处理结果, 超过了
if blood <= totalBlood and left <= showResultTime then
    redis.call("HDEL", dismissKey, sidSsid)
    return 3
end

return 1