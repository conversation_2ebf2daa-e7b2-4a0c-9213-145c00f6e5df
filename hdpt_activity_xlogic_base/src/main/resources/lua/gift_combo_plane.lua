
local combo_key = KEYS[1]
local combo_finish_key = KEYS[2]

local uid = ARGV[1]
local anchor_uid = ARGV[2]
local gift_id = ARGV[3]
local gift_num = ARGV[4]
local time = tonumber(ARGV[5])
local seq = ARGV[6]
local sid = ARGV[7]

local last_combo = redis.call('hget', combo_key, uid)

if last_combo then

    local combo = cjson.decode(last_combo)
    if combo.anchor_uid == anchor_uid and combo.gift_id == gift_id and combo.gift_num == gift_num then

        local finish_time = tonumber(redis.call('zscore', combo_finish_key, uid) or 0)
        if finish_time > time then

            combo.count = combo.count + 1
            combo.update_time = time
            redis.call('hset', combo_key, uid, cjson.encode(combo))
            redis.call('zadd', combo_finish_key, time + 5000, uid)
            return redis.call('hget', combo_key, uid)
        end
    end
end

local combo = {
    uid = uid,
    anchor_uid = anchor_uid,
    gift_id = gift_id,
    gift_num = gift_num,
    count = 1,
    start_time = time,
    seq = seq,
    sid = sid
}

redis.call('hset', combo_key, uid, cjson.encode(combo))

local finish = redis.call('zadd', combo_finish_key, time + 5000, uid)

if tonumber(finish) == 0 then
    return last_combo
end
