local settleFenceKey = KEYS[1]
local rewardKey = KEYS[2]

local uidAndDate = ARGV[1]
local currentRewards = tonumber(ARGV[2])

local alreadySettled = tonumber(redis.call('EXISTS', settleFenceKey) or 0)
-- 本日已结算
if (alreadySettled == 1) then
    return 0
end

local prevRewards = tonumber(redis.call('HGET', rewardKey, uidAndDate) or 0);
if(currentRewards > prevRewards) then
    redis.call('HSET',rewardKey,uidAndDate,currentRewards)
end
return 1

