---
--- Generated by Luanalysis
--- Created by z<PERSON><PERSON><PERSON>.
--- DateTime: 2023/8/24 14:12
--- 中控过任务



-- 取最小值
local function minOfTwoNumbers(num1, num2)
    if num1 < num2 then
        return num1
    else
        return num2
    end
end

-- 日志输出相关变量和函数
local logCntlFlag = '0'
local loginx = 0
local loginfo = {}
local function log(key, info)
    if logCntlFlag == '1' then
        loginx = loginx + 1
        loginfo[loginx] = { '(' .. loginx .. ') [' .. key .. ']', info }
    end
end



-- 判断字符串是否为空
local function empty(s)
    return s == nil or s == ''
end

--是否需要重置任务
local function canResetTask(curRound, recycleCount)
    --recycleCount 可循环完成次数

    -- 小于0：不限次数无限循环
    if recycleCount < 0 then
        return true
    end

    --0：不重置
    if recycleCount == 0 then
        return false
    end

    --大于0：重置的次数
    if curRound >= recycleCount then
        return false
    else
        return true
    end


end

--如循环任务则重置过任务次数
local function tryResetLastTask(taskIndex, totalTaskIndex, curRound, recycleCount)
    --不能重置任务次数
    if canResetTask(curRound, recycleCount) == false then
        return taskIndex
    end

    if taskIndex > totalTaskIndex then
        return 1
    end

    return taskIndex;
end

-- sort set key规则  keyPrefix:member:roletype:curRound:curTaskIndex
-- 返回当前贡献成员的key
local function saveContributeScore(updateTaskReq, result, curRound, curTaskIndex, curAdd, passCurTask)
    --检查有无需要保存贡献成员
    if updateTaskReq.contributeMember == nil or type(updateTaskReq.contributeMember) ~= 'table' then
        return
    end

    local keyTemplate = updateTaskReq.keyPrefix .. ':ctb:' .. updateTaskReq.member .. ':{roleType}:' .. curRound .. ':' .. curTaskIndex
    for key, value in pairs(updateTaskReq.contributeMember) do
        local contributeKey = string.gsub(keyTemplate, '{roleType}', tostring(key))
        -- 将当前贡献成员的值加入到对应的sorset中
        redis.call('ZINCRBY', contributeKey, curAdd, value)

        -- 返回结果添加 贡献key: memberid@roletype@isPassCurTask
        local resultKey = value .. '@' .. key .. '@' .. tostring(passCurTask)
        if result['contributeKey'][resultKey] == nil then
            result['contributeKey'][resultKey] = {}
        end
        --这里的顺序不要随意改变，按顺序加，调用方获取到后，如果有完成任务，排前面的贡献key就是完成任务的key!!!
        table.insert(result['contributeKey'][resultKey], contributeKey)

    end


end

--过任务
--curTaskIndex 当前任务索引，从1开始计数
local function pass_task(updateTaskReq, result, curTaskIndex, leftScore)
    log('pass_task', 'begin,curTaskIndex:' .. curTaskIndex .. ',leftScore:' .. leftScore .. ',curScoreTemp:' .. result['curScoreTemp'])
    local curRound = result['curRound']
    local recycleCount = tonumber(updateTaskReq.recycleCount or 0)
    --循环任务需要重置
    if recycleCount ~= 0 then
        result['curScoreTemp'] = result['curScoreTemp'] % updateTaskReq.taskConfig[#updateTaskReq.taskConfig];
    end

    for i = 1, #updateTaskReq.taskConfig do
        --循环任务次数配置小于0：不限次数无限循环

        --任务可循环完成次数设置为0，不可重置，只能过一轮
        if curRound > 0 and recycleCount == 0 then
            log('pass_task','curRound more than 0,recycleCount is 0 ,break')
            break
        end
        -- 循环任务次数配置大于0：超过重置的次数，中断
        if curRound > recycleCount and recycleCount > 0 then
            log('pass_task','curRound more than recycleCount,break')
            break
        end

        local curConfig = updateTaskReq.taskConfig[i]
        local configOffset = curConfig - result['curScoreTemp']
        if i >= curTaskIndex then
            local curAdd = minOfTwoNumbers(leftScore, configOffset)
            result['curScoreTemp'] = result['curScoreTemp'] + curAdd
            --能过当前任务
            local passCurTask = (result['curScoreTemp'] >= curConfig) and ((result['curScoreTemp'] - curAdd) < curConfig)

            saveContributeScore(updateTaskReq, result, curRound, i, curAdd, passCurTask)
            log('pass_task', 'startCal i:' .. i .. ',curTaskIndex:' .. curTaskIndex .. ',curRound:' .. curRound .. ',curScoreTemp:' .. result['curScoreTemp'])

            if passCurTask then
                curTaskIndex = tryResetLastTask(i + 1, #updateTaskReq.taskConfig, curRound, recycleCount)
            end

            log('pass_task', 'curTaskIndex:' .. curTaskIndex)

            result['currTaskIndex'] = curTaskIndex

            if i == #updateTaskReq.taskConfig and passCurTask then

                log('pass_task', 'complete round,index:' .. i)

                result['curRound'] = curRound + 1
                result['roundComplete'] = result['roundComplete'] + 1
            end

            leftScore = leftScore - curAdd

            log('pass_task', 'left score:' .. leftScore)

            if leftScore <= 0 then
                break
            end
        end
    end

    if leftScore > 0 and recycleCount ~= 0 then
        pass_task(updateTaskReq, result, curTaskIndex, leftScore)
    end


end

local function do_update_task(updateTaskReq)
    local keyPrefix = updateTaskReq.keyPrefix
    local seqKeyPrefix = updateTaskReq.seqKeyPrefix
    local seq = updateTaskReq.seq
    local repeatedCheckExpire = updateTaskReq.repeatedCheckExpire
    local saveCurResult = updateTaskReq.saveCurResult
    local member = updateTaskReq.member
    local addScore = updateTaskReq.score
    local seqkey = seqKeyPrefix .. ':' .. seq

    --seq检查
    if repeatedCheckExpire >= 0 then
        local updateOldResult = redis.call('GET', seqkey)
        if updateOldResult then
            local respInfo = {}
            --没保存结果，则返回seq重复
            if (saveCurResult ~= 1) then
                respInfo['code'] = 1
                return cjson.encode(respInfo)
            else
                return updateOldResult
            end
        end
    end

    --返回结果
    local result = {}
    result['code'] = 0
    --贡献成员key
    result['contributeKey'] = {}
    --当前分数
    local taskScoreZSetKey = keyPrefix .. ':score'
    local curScore = tonumber(redis.call('ZSCORE', taskScoreZSetKey, member) or 0)
    --当前分数临时变量，过任务的时候会逐步累加到afterScore位置
    result['curScoreTemp'] = curScore
    result['itemScore'] = addScore
    result['afterScore'] = curScore + addScore
    -- 当前任务轮数
    local curTaskHSetKey = keyPrefix .. ':info'
    local curRoundField = member .. '_last_round'
    local curRound = tonumber(redis.call('HGET', curTaskHSetKey, curRoundField) or 0)
    result['curRound'] = curRound
    -- result:本次送礼触发的 闯关轮数
    result['roundComplete'] = 0
    --开始站，从1开始
    local curTaskField = member .. '_last_task'
    local startTaskIndex = tonumber(redis.call('HGET', curTaskHSetKey, curTaskField) or 1)
    result['startTaskIndex'] = startTaskIndex
    --result:当前完成站，从1开始，1表示还没有过站
    result['currTaskIndex'] = startTaskIndex

    local leftScore = updateTaskReq.score

    pass_task(updateTaskReq, result, startTaskIndex, leftScore)

    ----保存更新任务结果
    --当前分数
    redis.call('ZINCRBY', taskScoreZSetKey, addScore, member)
    --当前关
    if result['currTaskIndex'] ~= startTaskIndex then
        redis.call('HSET', curTaskHSetKey, curTaskField, result['currTaskIndex'])
    end
    --当前轮数
    if result['curRound'] ~= curRound then
        redis.call('HSET', curTaskHSetKey, curRoundField, result['curRound'])
    end


    --保存整体结果
    if repeatedCheckExpire >= 0 then
        result['code'] = 1
        --如果重放的时候不用返回现场结构当前结果，则给个默认值1
        local value = '1'
        if (saveCurResult == 1) then
            value = cjson.encode(result)
        end
        redis.call('SET', seqkey, value)
        if repeatedCheckExpire > 0 then
            redis.call('EXPIRE', seqkey, updateTaskReq.repeatedCheckExpire)
        end

    end

    result['code'] = 0
    result['loginfo'] = loginfo
    --返回结果
    return cjson.encode(result);


end

local inputData = cjson.decode(ARGV[1])
logCntlFlag = inputData.logCntlFlag
return do_update_task(inputData)
