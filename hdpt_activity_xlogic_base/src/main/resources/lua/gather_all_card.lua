local cardUserKey = KEYS[1] --用户卡片的健
local allCarHasKey = KEYS[2] --合成标识
local gatherCardUids = KEYS[3] --收集合成用户标识
local uid=ARGV[6]
local flag=1

local last_combo = redis.call('hget', allCarHasKey, uid)
if last_combo then
    return 2 --已经合成过了
end

for i=1,5,1  do
    local result=tonumber(redis.call('hget',cardUserKey,ARGV[i]) or 0)
    if result < 1 then
        return 3; --不满足合成卡条件
    end
end

for i=1,5,1  do
    local result=tonumber(redis.call('hincrby',cardUserKey,ARGV[i],-1) or 0)
    if result < 0 then
        flag=0
    end
end

if flag==1 then
    redis.call('hset',allCarHasKey, uid, 'yes')
    redis.call('sadd',gatherCardUids,uid)
    return 1
end
if flag==0 then
    for i=1,5,1 do
        redis.call('hincrby',card<PERSON>ser<PERSON>ey,ARGV[i],1)
    end
end
return 0