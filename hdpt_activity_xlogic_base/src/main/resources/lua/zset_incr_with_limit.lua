
-- 要操作的成员
local member = ARGV[1]

-- 累加步长（可正、可负）
local step = tonumber(ARGV[2])

-- 限制值
local limit = tonumber(ARGV[3])

-- 提取当前分值
local oldScore = tonumber(redis.call('ZSCORE', KEYS[1], member) or 0)

if step >= 0 then
    -- 当 step为正时， incr 后不能超过 limit，
    if oldScore + step > limit then
        return  cjson.encode({-1, oldScore})
    end
else
    -- 当 step 为负时 incr 后不能低于 limit
    if oldScore + step < limit then
        return  cjson.encode({-2, oldScore})
    end
end

local newScore = tonumber(redis.call('ZINCRBY', KEYS[1], step, member) or 0)
return  cjson.encode({1, newScore})
