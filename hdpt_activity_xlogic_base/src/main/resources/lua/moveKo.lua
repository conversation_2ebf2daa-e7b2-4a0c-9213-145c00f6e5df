local key =  KEYS[1]
local memberKey = ARGV[1]

--local endTime = tonumber(ARGV[5])


-- 日志控制（生产应该关闭！以免影响性能）
local logCntlFlag = ARGV[2]
local dataKey = key.."_data"
local endKey = key.."_end"
local endKeyTemp = key.."_end_temp"
local countKey = key.."_count"


-- 日志输出相关变量和函数
local loginfo = {}
local function log(key, info)
 if logCntlFlag == '1' then
    loginfo[#loginfo+1] = { '(' .. #loginfo+1 .. ') [' .. key .. ']', info }
  end
end

local code = 0
local message = ''
local koInfo = redis.call('HGET', endKeyTemp,memberKey)
if koInfo ~= nil then
redis.call('RPUSH', endKey, koInfo)
log('RPUSH',endKey..' '..koInfo)
--保存ko次数
redis.call('ZINCRBY', countKey, 1, memberKey)
log('ZINCRBY',countKey..' 1 '..memberKey)
redis.call('HDEL', endKeyTemp, memberKey)
log('HDEL',endKeyTemp..' '..memberKey)
message = memberKey..' move success .'
else
message = memberKey..' can not find koInfo form '..endKeyTemp..'.'
code = 1
end

local returnData = {}
returnData["code"] = code
returnData["message"] = message
returnData["logInfo"] = loginfo
return cjson.encode(returnData)



