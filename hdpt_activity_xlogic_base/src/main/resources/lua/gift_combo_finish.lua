
local combo_key = KEYS[1]
local combo_finish_key = KEYS[2]

local time = tonumber(ARGV[1])

local finish_uid = redis.call('zrangebyscore', combo_finish_key, 0, time)
if finish_uid then
    redis.call('zremrangebyscore', combo_finish_key, 0, time)
    local finish_list = {}
    for i = 1, table.getn(finish_uid) do
        finish_list[i] = redis.call('hget', combo_key, finish_uid[i])
    end
    return finish_list
end
