local key = KEYS[1]
local duplicatedCheckKey = KEYS[2]
local member = ARGV[1]
local timestamp = tonumber(ARGV[2])
local maxLen = tonumber(ARGV[3])
local checkMember = ARGV[4]

-- 判断字符串是否为空
local function empty(s)
    return s == nil or s == ''
end

-- 长度检查
local currLen = tonumber(redis.call('ZCARD', key) or 0)
if currLen >= maxLen then
    return -1
end

-- 是否已经在要加入的key中
if redis.call('ZRAN<PERSON>', key, member) then
    return -2
end

-- 全局防重检查（不限于在 key 中）
if not empty(duplicatedCheckKey) then
    if redis.call('HEXISTS', duplicated<PERSON>heck<PERSON><PERSON>, member) == 1 then
        return -3
    end
end

-- 加入到key中 + 加入到 duplicatedCheckKey 中
redis.call('ZADD', key, timestamp, member)
if not empty(duplicated<PERSON><PERSON><PERSON><PERSON><PERSON>) then
    redis.call('HSET', duplicated<PERSON><PERSON><PERSON><PERSON><PERSON>, member, checkMember)
end

return currLen + 1
