local key = KEYS[1]
local field = KEYS[2]
local newValue = tonumber(ARGV[1])
local interval = tonumber(ARGV[2] or 0)

--排队逻辑：旧值与新值的差大于间隔说明不用排队，用新值替换旧值，返回新值，否则旧值递增，返回递增后的值
local oldValue = tonumber(redis.call('HGET', key, field) or 0)
if newValue-oldValue > interval then
    redis.call('HSET', key, field, newValue)
    return newValue
else
    local newScore = oldValue + interval
    redis.call('HSET', key, field, newScore)
    return newScore
end

