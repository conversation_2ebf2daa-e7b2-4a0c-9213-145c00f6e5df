
local key = KEYS[1]

local uid = ARGV[1]
local accumulatedJudge = ARGV[2]
local accumulatedData = ARGV[3]
local time = tonumber(ARGV[4])
local seq = ARGV[5]
local daleyTime = tonumber(ARGV[6])

local finishTimeKey = key .. '_time'

local lastRecord = redis.call('hget', key, uid)

if lastRecord then

    local record = cjson.decode(lastRecord)
    --判断是否需要累积
    if record.accumulatedJudge == accumulatedJudge then
        --需要累积并且累积时间还没到，更新数据和时间
        local finishTime = tonumber(redis.call('zscore', finishTimeKey, uid) or 0)
        if finishTime > time then

            record.count = record.count + 1
            record.updateTime = time
            record.seq = record.seq .. ',' ..seq
            redis.call('hset', key, uid, cjson.encode(record))
            redis.call('zadd', finishTimeKey, time + daleyTime, uid)
            return nil
        end
    end
end
--缓存新的记录，返回用户上一条记录
 local newRecord = {
     uid = uid,
     accumulatedJudge = accumulatedJudge,
     data = accumulatedData,
     count = 1,
     startTime = time,
     updateTime = time,
     seq = seq
 }

redis.call('hset', key, uid, cjson.encode(newRecord))

local finish = redis.call('zadd', finishTimeKey, time + daleyTime, uid)

if tonumber(finish) == 0 then
    return lastRecord
end
