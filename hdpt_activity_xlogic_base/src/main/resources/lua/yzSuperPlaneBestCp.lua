local cpRank = KEYS[1]
local cp = KEYS[2]

local uid = ARGV[1]
local captain = ARGV[2]
local score = tonumber(ARGV[3])
local time = tonumber(ARGV[4])
local channel = ARGV[5]

local srcScore = math.floor(tonumber(redis.call('ZSCORE', cpRank, uid) or 0))
if score > srcScore then
    redis.call('ZADD', cpRank, score + time, uid)
    redis.call('HSET', cp, uid, captain..'_'..channel)
    return true
end
return false
