
local key = KEYS[1]
local time = tonumber(ARGV[1])
local finishTimeKey = key .. '_time'

local finishUids = redis.call('zrangebyscore', finishTimeKey, 0, time)
if finishUids then
    redis.call('zremrangebyscore', finishTimeKey, 0, time)
    local finishList = {}
    for i = 1, table.getn(finishUids) do
        finishList[i] = redis.call('hget', key, finishUids[i])
        redis.call('hdel', key, finishUids[i])
    end
    return finishList
end
