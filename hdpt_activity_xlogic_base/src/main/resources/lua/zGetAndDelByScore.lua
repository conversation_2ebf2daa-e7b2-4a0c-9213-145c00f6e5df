local key =  KEYS[1]
local min = ARGV[1]
local max = ARGV[2]
local count = ARGV[3]
local logCntlFlag = ARGV[4]

-- 日志输出相关变量和函数
local loginfo = {}
local function log(key, info)
 if logCntlFlag == '1' then
    loginfo[#loginfo+1] = { '(' .. #loginfo+1 .. ') [' .. key .. ']', info }
  end
end

local result = redis.call('ZRANGEBYSCORE', key, min,max,"limit", 0, count)
log('ZRANGEBYSCORE',key..' '..min..' '..max..' limit  0 '..count..' '..cjson.encode(result))
for index, member in ipairs(result) do
    redis.call('ZREM', key, member)
    log('ZREM',key..' '..member)
end
local returnData = {}
returnData["result"] = result
returnData["logInfo"] = loginfo
return cjson.encode(returnData)



