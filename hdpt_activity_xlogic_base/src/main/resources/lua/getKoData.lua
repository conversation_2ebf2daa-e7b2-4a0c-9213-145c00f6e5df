local key =  KEYS[1]
local method =  ARGV[1]

local dataKey = key.."_data"
local endKey = key.."_end"
local koAccomplish = {}
local existMap = {}
local result = ''


--ko string 转成自动添加开始时间
local function addStartTimeToPkInfoString(pkInfoString, startTime)
    local pkInfo = cjson.decode(pkInfoString)
    pkInfo["startTime"] = startTime
    return cjson.encode(pkInfo)
end

local function getAccomplishKoList(endKey)
    local existMap = {}
    local results = {}
    local count = redis.call('LLEN', endKey)
    local data = redis.call('LRANGE', endKey, 0, count)
    local returnData = {}
    returnData["data"] = data
    returnData["message"] = ''
    return cjson.encode(returnData)
end


local function getDoingKoList(key,dataKey)
    local existMap = {}
    local koInfos = {}
    local data = redis.call('ZREVRANGE', key,0, -1, 'withscores')

    local count =  #data/2
    if count>0 then

        for i=1, count do
            local memberKey = data[i*2-1]
            local startTime = data[i*2]
            existMap[memberKey] = startTime
        end

        local detailData = redis.call('HGETALL', dataKey)
        local detailDataCount =  #detailData/2
        for i=1, detailDataCount do
            local key = detailData[i*2-1]
            local value = detailData[i*2]
            local startTime = existMap[key]
            local kpInfoString = addStartTimeToPkInfoString(value,startTime)
            koInfos[#koInfos + 1]= kpInfoString
        end
    end
    local returnData = {}
    returnData["data"] = koInfos
    returnData["message"] = ''
    return cjson.encode(returnData)
end

if method ~= 'doing' and  method ~= 'accomplish' then
     local returnData = {}
     returnData["data"] = {}
     returnData["message"] = 'method error,method has to be either \"doing\" or \"accomplish\"'
     result = cjson.encode(returnData)
else
    if method == 'doing' then
        result = getDoingKoList(key,dataKey)
    end
    if method == 'accomplish'then
        result = getAccomplishKoList(endKey)
    end
end
return result
