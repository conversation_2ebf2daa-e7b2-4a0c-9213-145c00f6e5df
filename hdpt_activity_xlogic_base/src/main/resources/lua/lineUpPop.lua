local sortSetKey = KEYS[1]
local nowSeconds = ARGV[1]

local groupInfoKey = sortSetKey .. '.group_info'

local content = ''

local subSortSetKey
local groupInfo
local groupInfos = redis.call('ZRANGE', groupInfoKey, 0, 0)
if (groupInfos ~= false and #groupInfos > 0) then
    subSortSetKey = sortSetKey .. ':' .. groupInfos[1]
    groupInfo = groupInfos[1]
else
    return content
end

local popData = redis.call('ZRANGEBYSCORE', subSortSetKey, 0, nowSeconds, 'WITHSCORES', 'LIMIT', 0, 1)
if (popData ~= false and #popData ~= 0) then
    local id = popData[1]
    local contentKey = subSortSetKey .. ':' .. id
    content = redis.call('get', contentKey)

    --最近一次出队时间,30天有效期
    local lastPopTimeKey = subSortSetKey .. '.lastPopTime'
    redis.call('SET', lastPopTimeKey, nowSeconds)
    redis.call('EXPIRE', lastPopTimeKey, 2592000)
    --分组分组出队公平,出了后把优先级调到最后
    redis.call('ZADD', groupInfoKey, nowSeconds, groupInfo)
    --删除弹出的数据
    redis.call('ZREM', subSortSetKey, id)
    redis.call('DEL', contentKey)
else
    local amount = tonumber(redis.call('ZCARD', subSortSetKey) or 0)
    if amount == 0 then
        redis.call('ZREM', groupInfoKey, groupInfo)
    else
        --有数据，但是未到出队时间的放到后面
        redis.call('ZADD', groupInfoKey, nowSeconds, groupInfo)
    end
end

return content