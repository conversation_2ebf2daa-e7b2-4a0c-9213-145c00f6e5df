spring.application.name=hdzk_activity${group}

spring.main.allow-bean-definition-overriding=true

#spring redis session, can be none, redis, ...(need redis 2.8 + version support)
spring.session.store-type=none
logging.config=classpath:log4j2-deploy.xml
logging.register-shutdown-hook=false
local.cache.refresh.cron=3 * * * * ?

#eagle eye report setting
eagle_eye_appver=2.5.0
eagle_eye_srvver=3.0.0
eagle_eye_scales=5,10,20,30,50,100,200,300,500,800,1000,2000,3000,5000,8000,15000,20000,40000,60000,120000

mybatis.mapper-locations=classpath:mapper/**/*.xml
mybatis.configuration.map-underscore-to-camel-case=true

# redis config
gameecology.redis.sentinel.master=ge_activity_redis_001
gameecology.redis.sentinel.nodes=group1006-wx-sentinel.yy.com:20091,group1006-bj-sentinel.yy.com:20091,group1006-sz-sentinel.yy.com:20091
gameecology.redis.timeout=5s
gameecology.redis.lettuce.pool.max-active=25
gameecology.redis.lettuce.pool.max-idle=25
gameecology.redis.lettuce.pool.time-between-eviction-runs=60s

# redis config for global control or cache - added by guoliping/2020-12-23
gameecology.global.redis.sentinel.master=ge_activity_redis_001
gameecology.global.redis.sentinel.nodes=group1006-wx-sentinel.yy.com:20091,group1006-bj-sentinel.yy.com:20091,group1006-sz-sentinel.yy.com:20091
gameecology.global.redis.database=0
gameecology.global.redis.timeout=5s
gameecology.global.redis.lettuce.pool.max-active=25
gameecology.global.redis.lettuce.pool.max-idle=25
gameecology.global.redis.lettuce.pool.time-between-eviction-runs=60s


# redis group config, The first group saves some old data and public data. Try not to use them
gameecology.group0.redis.sentinel.master=ge_activity_redis_001
gameecology.group0.redis.sentinel.nodes=group1006-wx-sentinel.yy.com:20091,group1006-bj-sentinel.yy.com:20091,group1006-sz-sentinel.yy.com:20091
gameecology.group0.redis.database=0
gameecology.group0.redis.timeout=5s
gameecology.group0.redis.lettuce.pool.max-active=25
gameecology.group0.redis.lettuce.pool.max-idle=25
gameecology.group0.redis.lettuce.pool.time-between-eviction-runs=60s

gameecology.group1.redis.sentinel.master=ge_activity_redis_group_001
gameecology.group1.redis.sentinel.nodes=group1014-wx-sentinel.yy.com:20039,group1014-bj-sentinel.yy.com:20039,group1014-sz-sentinel.yy.com:20039
gameecology.group1.redis.database=0
gameecology.group1.redis.timeout=5s
gameecology.group1.redis.lettuce.pool.max-active=25
gameecology.group1.redis.lettuce.pool.max-idle=25
gameecology.group1.redis.lettuce.pool.time-between-eviction-runs=60s

gameecology.group2.redis.sentinel.master=ge_activity_redis_group_002
gameecology.group2.redis.sentinel.nodes=group1014-wx-sentinel.yy.com:20039,group1014-bj-sentinel.yy.com:20039,group1014-sz-sentinel.yy.com:20039
gameecology.group2.redis.database=0
gameecology.group2.redis.timeout=5s
gameecology.group2.redis.lettuce.pool.max-active=25
gameecology.group2.redis.lettuce.pool.max-idle=25
gameecology.group2.redis.lettuce.pool.time-between-eviction-runs=60s

gameecology.group3.redis.sentinel.master=ge_activity_redis_group_003
gameecology.group3.redis.sentinel.nodes=group1014-wx-sentinel.yy.com:20039,group1014-bj-sentinel.yy.com:20039,group1014-sz-sentinel.yy.com:20039
gameecology.group3.redis.database=0
gameecology.group3.redis.timeout=5s
gameecology.group3.redis.lettuce.pool.max-active=25
gameecology.group3.redis.lettuce.pool.max-idle=25
gameecology.group3.redis.lettuce.pool.time-between-eviction-runs=60s

gameecology.group4.redis.sentinel.master=ge_activity_redis_group_004
gameecology.group4.redis.sentinel.nodes=group1014-wx-sentinel.yy.com:20039,group1014-bj-sentinel.yy.com:20039,group1014-sz-sentinel.yy.com:20039
gameecology.group4.redis.database=0
gameecology.group4.redis.timeout=5s
gameecology.group4.redis.lettuce.pool.max-active=25
gameecology.group4.redis.lettuce.pool.max-idle=25
gameecology.group4.redis.lettuce.pool.time-between-eviction-runs=60s


gameecology.group101.redis.sentinel.master=hdpt_zk_pika_001
gameecology.group101.redis.sentinel.nodes=group1015-wx-sentinel.yy.com:20066,group1015-bj-sentinel.yy.com:20066,group1015-fs-sentinel.yy.com:20066
gameecology.group101.redis.database=0
gameecology.group101.redis.timeout=5s
gameecology.group101.redis.lettuce.pool.max-active=25
gameecology.group101.redis.lettuce.pool.max-idle=25
gameecology.group101.redis.lettuce.pool.time-between-eviction-runs=60s

########### Gameecology Bridge START ############################################################### # token:yzbb_gb_is_good
#thrift Gameecology Bridge client config, \u8D85\u65F6\u5355\u4F4D\uFF1A \u79D2, added by guoliping/2019-08-26
thrift_bridge_gameecologyproxy_client_registry=s2s://yzbb_gameecology_proxy_bridge:b32bc30074d5edd593f7eb1c7075d1497423e73c9db3d5dd474ccc2db3d9ef98ceb06da45648453bc36372280cb9e65e@0.0.0.0
thrift_bridge_gameecologyproxy_client_timeout=300
thrift_bridge_gameecologyproxy_client_threads=10
########### Gameecology Bridge END ###############################################################

#thrift jiaoyou proxy client
thrift_jiaoyou_proxy_client_registry=s2s://fts_gameecology_bridge:3b5d8a0db2d8bb8a23d6f67c229252cc071d64c74666be9afd93c381d3a3314a15966cbd9ab41d0de67586e6bc8fe7de@0.0.0.0
thrift_jiaoyou_proxy_client_s2sname=fts_gameecology_bridge
thrift_jiaoyou_proxy_client_timeout=300
thrift_jiaoyou_proxy_client_thread=10

thrift_bridge_gamebabyactivity_client_registry=s2s://yzbb_gamebaby_activity_bridge:b32bc30074d5edd570838a011651dfdf73fb41cf032028cb4a1fdb902b6c635a40f1b5ea4e752448476836afecb3a1d4@0.0.0.0
thrift_bridge_gamebabyactivity_client_s2sname=yzbb_gamebaby_activity_bridge
thrift_bridge_gamebabyactivity_client_timeout=300
thrift_bridge_gamebabyactivity_client_threads=10

turnoverService_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
turnoverService_client_s2sname=to_service
turnoverContract_client_s2sname=to_contract
turnoverGiftbagStat_client_s2sname=to_finance
turnoverService_client_timeout=10
turnoverService_client_threads=10
turnoverService_http_host=https://turnover.yy.com

lightup.weideng.thrift.urls=thrift://eximinfo.service.yy.com:1077
lightup.weideng.thrift.timeout=10

thrift_fts_zhuiya_recommond_client_s2s_name=fts_zhuiya_recommend_v2

#\u6D3B\u52A8\u4E2D\u53F0ranking\u8FDB\u7A0Bs2s Name
hdztRanking_client_s2sname=hdzt_ranking${group}${historyS2SSuffix:}
#\u6D3B\u52A8\u4E2D\u53F0award\u8FDB\u7A0Bs2s Name
thrift.client.hdztaward.s2sname=hdzt_award${group}${historyS2SSuffix:}

# kafka hdzt
#------------------------------------------------------------------------------------------
kafka.hdzt.ranking.updating.topic=hdzt${group}_ranking_updating_topic
kafka.hdzt.ranking.events.topic=hdzt${group}_ranking_events_topic
kafka.hdzt.award.issueNotice.topic=hdzt${group}_award_issueNotice_topic
kafka.hdzt.geact.consumer.group=hdzt${group}_ge_consumer_group
###########################################################################################

thrift_anti_cheat_gn_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
thrift_anti_cheat_gn_client_s2sname=ysec_anti_cheat_gn
thrift_anti_cheat_gn_client_timeout=3
thrift_anti_cheat_gn_client_threads=3

#for ysec fts_props_agent thrift client settings, added by guoliping/2020-06-02, \uFFFD\uFFFD\uFFFD\uFFFD \u05A3\uFFFD\uFFFD \uFFFD\uFFFD\uFFFD\uFFFD\u05A7\uFFFD\uFFFD
thrift_fts_props_agent_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
thrift_fts_props_agent_client_s2sname=fts_props_agent

ftsBaseInfoBridge_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
ftsBaseInfoBridge_client_s2sname=fts_base_info_bridge
ftsBaseInfoBridge_client_timeout=10
ftsBaseInfoBridge_client_threads=10

hdztRanking_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
hdztRanking_client_timeout=10
hdztRanking_client_threads=10

thrift.client.hdztaward.registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0

actPwSupportService_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
actPwSupportService_client_s2sname=peiwan_thrift
actPwSupportService_client_timeout=3
actPwSupportService_client_threads=10

########## zhuiwan_prize_issue config start###########
thrift_zhuiwan_prize_issue_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
thrift_zhuiwan_prize_issue_client_timeout=30
thrift_zhuiwan_prize_issue_client_threads=10
thrift_zhuiwan_prize_issue_client_s2s_name=zhuiya_hdzt_thrift
########### zhuiwan_prize_issue config end###########

########## fts_compere_group config start###########
thrift_fts_compere_group_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
thrift_fts_compere_group_client_timeout=30
thrift_fts_compere_group_client_threads=10
thrift_fts_compere_group_client_s2s_name=fts_compere_group
########### fts_compere_group config end###########

thrift_fts_party_grade_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
thrift_fts_party_grade_client_s2sname=fts_party_grade

########## fts_group_center config start###########
thrift_fts_group_center_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
thrift_fts_group_center_client_timeout=30
thrift_fts_group_center_client_threads=10
thrift_fts_group_center_client_s2s_name=fts_group_center
########### fts_group_center end###########

########## fts_sensitive_word config start###########
thrift_fts_sensitive_word_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
thrift_fts_sensitive_word_client_timeout=30
thrift_fts_sensitive_word_client_threads=10
thrift_fts_sensitive_word_client_s2s_name=fts_sensitive
########### fts_sensitive_word end###########

thrift_fts_supplement_client_s2s_name=fts_supplement

########### fts_recommend_data_base start #########
thrift.client.fts_recmd_data.registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
thrift.client.fts_recmd_data.s2sname=fts_recommend_data_base
thrift.client.fts_recmd_data.timeout=30
thrift.client.fts_recmd_data.threads=10
########### fts_recommend_data_base end #########

########### fts_recommend_data_base start #########
thrift.client.fts_room_manager.registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
thrift.client.fts_room_manager.s2sname=fts_room_manager
thrift.client.fts_room_manager.timeout=30
thrift.client.fts_room_manager.threads=10
########### fts_recommend_data_base end #########


########## fts_live_helper config start###########
thrift_fts_live_helper_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
thrift_fts_live_helper_client_timeout=30
thrift_fts_live_helper_client_threads=10
thrift_fts_live_helper_client_s2s_name=fts_live_helper
########### fts_live_helper end###########

zhuiwanCommonList_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
zhuiwanCommonList_client_s2sname=common_list
zhuiwanCommonList_client_timeout=10
zhuiwanCommonList_client_threads=10

saibaoService_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
saibaoService_client_s2sname=game_gateway
saibaoService_client_timeout=10
saibaoService_client_threads=10

thrift_fts_chpopular_client_s2s_name=fts_ch_popular

########\u00BD\u00BB\u00D3\u00D1kafka############
kafka.jiaoyou.wx.server=kafkawx006-core001.yy.com:8104,kafkawx006-core002.yy.com:8104,kafkawx006-core003.yy.com:8104

kafka.jiaoyou.sz.server=kafkasz004-core001.yy.com:8107,kafkasz004-core002.yy.com:8107,kafkasz004-core003.yy.com:8107

kafka.jiaoyou-wx.producer.bootstrap-servers=kafkawx006-core001.yy.com:8104,kafkawx006-core002.yy.com:8104,kafkawx006-core003.yy.com:8104
kafka.jiaoyou-wx.producer.acks=1

kafka.jiaoyou-sz.producer.bootstrap-servers=kafkasz004-core001.yy.com:8107,kafkasz004-core002.yy.com:8107,kafkasz004-core003.yy.com:8107
kafka.jiaoyou-sz.producer.acks=1

########\u00BD\u00BB\u00D3\u00D1\u00CB\u00CD\u00C0\u00F1\u00CA\u00C2\u00BC\u00FE kafka############
kafka.jiaoyou.props.wx.server=kafkawx006-core001.yy.com:8104,kafkawx006-core002.yy.com:8104,kafkawx006-core003.yy.com:8104
kafka.jiaoyou.props.wx.topic=propsUseInfoCross
kafka.jiaoyou.props.sz.server=kafkasz004-core001.yy.com:8107,kafkasz004-core002.yy.com:8107,kafkasz004-core003.yy.com:8107
kafka.jiaoyou.props.sz.topic=propsUseInfoCross

########\u00BD\u00BB\u00D3\u00D1\u00B8\u00C7\u00D5\u00C2\u00CA\u00C2\u00BC\u00FE kafka############
kafka.jiaoyou.seal.wx.server=kafkawx006-core001.yy.com:8104,kafkawx006-core002.yy.com:8104,kafkawx006-core003.yy.com:8104
kafka.jiaoyou.seal.wx.topic=propsSealInfoCross
kafka.jiaoyou.seal.sz.server=kafkasz004-core001.yy.com:8107,kafkasz004-core002.yy.com:8107,kafkasz004-core003.yy.com:8107
kafka.jiaoyou.seal.sz.topic=propsSealInfoCross

########\u00BD\u00BB\u00D3\u00D1\u00D6\u00F7\u00B3\u00D6\u00BF\u00AA\u00B2\u00A5\u00CA\u00C2\u00BC\u00FE kafka############
kafka.jiaoyou.push_live.wx.server=kafkawx006-core001.yy.com:8104,kafkawx006-core002.yy.com:8104,kafkawx006-core003.yy.com:8104
kafka.jiaoyou.push_live.wx.topic=msgPushCompereLiveNotifyCross
kafka.jiaoyou.push_live.sz.server=kafkasz004-core001.yy.com:8107,kafkasz004-core002.yy.com:8107,kafkasz004-core003.yy.com:8107
kafka.jiaoyou.push_live.sz.topic=msgPushCompereLiveNotifyCross

########\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u04B6\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u00BC\uFFFD kafka############
kafka.jiaoyou.fight_end.wx.server=kafkawx006-core001.yy.com:8104,kafkawx006-core002.yy.com:8104,kafkawx006-core003.yy.com:8104
kafka.jiaoyou.fight_end.wx.topic=appChannelFightEndInfoCross
kafka.jiaoyou.fight_end.sz.server=kafkasz004-core001.yy.com:8107,kafkasz004-core002.yy.com:8107,kafkasz004-core003.yy.com:8107
kafka.jiaoyou.fight_end.sz.topic=appChannelFightEndInfoCross

kafka.jiaoyou.cross.producer.bootstrap-servers=kafkawx011-core001.yy.com:8110,kafkawx011-core002.yy.com:8110,kafkawx011-core003.yy.com:8110

########\u00D3\u00AA\u00CA\u00D5\u00B3\u00E4\u00D6\u00B5\u00D6\u00A7\u00B8\u00B6\u00BD\u00E1\u00B9\u00FB\u00CD\u00A8\u00D6\u00AA kafka############
kafka.turnover.charge.server=kafkawx012-core001.yy.com:8105,kafkawx012-core002.yy.com:8105,kafkawx012-core003.yy.com:8105

kafka.zhuiwan.producer.bootstrap-servers=kafkawx006-core001.yy.com:8112,kafkawx006-core002.yy.com:8112,kafkawx006-core003.yy.com:8112
kafka.zhuiwan.producer.acks=1

kafka.zhuiwan.consumer.bootstrap-servers=kafkawx006-core001.yy.com:8112,kafkawx006-core002.yy.com:8112,kafkawx006-core003.yy.com:8112
kafka.zhuiwan.consumer.auto-offset-reset=latest

######## \u00C5\u00E3\u00CD\u00E6http\u00B7\u00FE\u00CE\u00F1\u00C5\u00E4\u00D6\u00C3############
peiwan.http_host=https://peipei.yy.com
peiwan.http_salt=Wocm1g0UQhn1ONc0X4OY

## Yrpc
## https://git.yy.com/midwares/yrpc/yrpc-java-new/-/blob/master/dubbo-docs/v2/demo.nythrift.boot.md
dubbo.application.name=gameecology_java
dubbo.scan.base-packages=com.yy.gameecology.activity.worker.server
dubbo.registries.yrpc-reg.id=yrpc-reg
dubbo.registries.yrpc-reg.address=s2s://meta.yy.com
dubbo.registries.yrpc-reg.username=${s2s.name}
dubbo.registries.yrpc-reg.password=${s2s.key}

# thrift reg
dubbo.registries.thrift-reg.id=thrift-reg
dubbo.registries.thrift-reg.address=s2s://meta.yy.com
dubbo.registries.thrift-reg.username=${s2s.hdzk.server.name}
dubbo.registries.thrift-reg.password=${s2s.hdzk.server.key}
dubbo.protocols.attach_nythrift.name=attach_nythrift
dubbo.protocols.attach_nythrift.port=10222

## mobserv cast
## https://git.yy.com/fanyuwen/yrpc-java-new/-/blob/master/dubbo-docs/v2/demo.cast.boot.md
mobserv.s2sname=mobsrv
mobserv.registryIds=yrpc-reg
mobserv.appId=15013
mobserv.connections=10
mobserv.timeout=3000
mobserv.retries=1

#fostress config
fostress.ack-mode=kafka
fostress.bootstrap-servers=kafkawx019-core001.yy.com:8106,kafkawx019-core002.yy.com:8106,kafkawx019-core003.yy.com:8106
fostress.me=gameecology_activity${group}
fostress.center=fostress_center_server
fostress.reg-address=s2s://meta.yy.com
fostress.reg-username=${s2s.name}
fostress.reg-password=${s2s.key}

## micrometer config
## https://doc.yy.com/pages/viewpage.action?pageId=224063763
management.metrics.tags.application=hdzk_activity${group}
management.metrics.tags.env=prod
#management.server.port=8081
#management.endpoints.web.exposure.include=prometheus
#management.endpoints.enabled-by-default=false
#management.endpoint.prometheus.enabled=true

yboot.metrics.appName=hdzk_activity
yboot.metrics.serviceName=hdzk_activity${group}

yboot.thread-pool.pools.general.core-threads=20
yboot.thread-pool.pools.general.max-threads=150
yboot.thread-pool.pools.general.queues=10000
yboot.thread-pool.pools.general.prefix=general-pool
yboot.thread-pool.pools.general.alive=60000
yboot.thread-pool.pools.general.rejected-class=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy

yboot.thread-pool.pools.important.core-threads=50
yboot.thread-pool.pools.important.max-threads=150
yboot.thread-pool.pools.important.queues=10000
yboot.thread-pool.pools.important.prefix=important-pool
yboot.thread-pool.pools.important.alive=60000
yboot.thread-pool.pools.important.rejected-class=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy

yboot.thread-pool.pools.sys_important.core-threads=150
yboot.thread-pool.pools.sys_important.max-threads=150
yboot.thread-pool.pools.sys_important.queues=30000
yboot.thread-pool.pools.sys_important.prefix=sys-important-pool
yboot.thread-pool.pools.sys_important.alive=60000
yboot.thread-pool.pools.sys_important.rejected-class=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy

yboot.thread-pool.pools.delay_queue_consumer.core-threads=20
yboot.thread-pool.pools.delay_queue_consumer.max-threads=150
yboot.thread-pool.pools.delay_queue_consumer.queues=0
yboot.thread-pool.pools.delay_queue_consumer.prefix=delay-queue-pool
yboot.thread-pool.pools.delay_queue_consumer.alive=60000
yboot.thread-pool.pools.delay_queue_consumer.rejected-class=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy

yboot.thread-pool.pools.method_log.core-threads=5
yboot.thread-pool.pools.method_log.max-threads=100
yboot.thread-pool.pools.method_log.queues=10000
yboot.thread-pool.pools.method_log.prefix=method-log-pool
yboot.thread-pool.pools.method_log.alive=60000

yboot.thread-pool.pools.online_channel.core-threads=30
yboot.thread-pool.pools.online_channel.max-threads=200
yboot.thread-pool.pools.online_channel.queues=1000
yboot.thread-pool.pools.online_channel.prefix=online-channel-pool
yboot.thread-pool.pools.online_channel.alive=60000
yboot.thread-pool.pools.online_channel.rejected-class=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy

yboot.thread-pool.pools.eagle_report.core-threads=30
yboot.thread-pool.pools.eagle_report.max-threads=100
yboot.thread-pool.pools.eagle_report.queues=20000
yboot.thread-pool.pools.eagle_report.prefix=eagle-report-pool
yboot.thread-pool.pools.eagle_report.alive=60000

yboot.thread-pool.pools.kafka_msg_handler.core-threads=8
yboot.thread-pool.pools.kafka_msg_handler.max-threads=16
yboot.thread-pool.pools.kafka_msg_handler.queues=20000
yboot.thread-pool.pools.kafka_msg_handler.prefix=kafka-msg-handler-pool
yboot.thread-pool.pools.kafka_msg_handler.alive=60000
yboot.thread-pool.pools.kafka_msg_handler.rejected-class=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy

yboot.thread-pool.pools.cmpt_build_layer.core-threads=30
yboot.thread-pool.pools.cmpt_build_layer.max-threads=60
yboot.thread-pool.pools.cmpt_build_layer.queues=5000
yboot.thread-pool.pools.cmpt_build_layer.prefix=cmpt-build-layer-pool
yboot.thread-pool.pools.cmpt_build_layer.alive=60000
yboot.thread-pool.pools.cmpt_build_layer.rejected-class=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy

yboot.thread-pool.pools.bro_layer_schedule.core-threads=10
yboot.thread-pool.pools.bro_layer_schedule.max-threads=20
yboot.thread-pool.pools.bro_layer_schedule.queues=1000
yboot.thread-pool.pools.bro_layer_schedule.prefix=bro-layer-schedule-pool
yboot.thread-pool.pools.bro_layer_schedule.alive=60000
yboot.thread-pool.pools.bro_layer_schedule.rejectedClass=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy
yboot.thread-pool.pools.bro_layer_schedule.type=scheduled

yboot.thread-pool.pools.aggregation_schedule.core-threads=10
yboot.thread-pool.pools.aggregation_schedule.max-threads=20
yboot.thread-pool.pools.aggregation_schedule.queues=1000
yboot.thread-pool.pools.aggregation_schedule.prefix=aggregation-schedule-pool
yboot.thread-pool.pools.aggregation_schedule.alive=60000
yboot.thread-pool.pools.aggregation_schedule.rejectedClass=java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy
yboot.thread-pool.pools.aggregation_schedule.type=scheduled

yy.env=prod
yy.app-name=${spring.application.name}
yy.webdb.enable=true
yy.webdb.auth-user=1223003332
yy.webdb.auth-key=b09b7bf5_d243
# core pool size
yy.webdb.pool-size=20

yy.cul.enable=true
yy.cul.auth-user=1223003332
yy.cul.auth-key=b09b7bf5_d243
yy.cul.pool-size=20

kafka.ge.ranking.events.outer.topic=ge_ranking_events_outer_topic_${group}

kafka.hdzk.wzry.game.events.topic=hdzk${group}_wzry_game_events_topic

kafka.hdzk.common.events.topic=hdzk${group}_common_events_topic

// &offset=0&size=100
skill.card.white.list.request.url=https://common-list.yy.com/list/common/sid/list?business=zhuiwan&module=peiwan_plugin_rank
audit.mms.text.bulk.url=https://twapi.yy.com/skynet/bulk/api


thrift_zhuiya_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
thrift_zhuiya_client_timeout=30
thrift_zhuiya_client_threads=10
thrift_zhuiya_client_s2s_name=zhuiya_server_thrift

thrift_skillcard_client_registry=s2s://ge_access_s2s_acct:4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1@0.0.0.0
thrift_skillcard_client_timeout=30
thrift_skillcard_client_threads=10
thrift_skillcard_client_s2s_name=skillcard_server_thrift

kafka.zhuiya.producer.bootstrap-servers=kafkawx006-core001.yy.com:8112,kafkawx006-core002.yy.com:8112,kafkawx006-core003.yy.com:8112
kafka.zhuiya.producer.acks=1


kafka.gamebaby.server=kafkawx012-core003.yy.com:8103,kafkawx012-core002.yy.com:8103,kafkawx012-core001.yy.com:8103
kafka.gamebaby.group=gameecology_act_${group}

kafka.hdpt.wx.server=kafkawx012-core001.yy.com:8106,kafkawx012-core002.yy.com:8106,kafkawx012-core003.yy.com:8106
kafka.hdpt.wx.producer.bootstrap-servers=kafkawx012-core001.yy.com:8106,kafkawx012-core002.yy.com:8106,kafkawx012-core003.yy.com:8106
kafka.hdpt.wx.producer.acks=1
kafka.hdpt.wx.group=gameecology_act_${group}

kafka.hdpt.wx-xdc.server=kafkasz012-core001.yy.com:8101,kafkasz012-core002.yy.com:8101,kafkasz012-core003.yy.com:8101
kafka.hdpt.wx-xdc.producer.bootstrap-servers=kafkasz012-core001.yy.com:8101,kafkasz012-core002.yy.com:8101,kafkasz012-core003.yy.com:8101
kafka.hdpt.wx-xdc.producer.acks=1
kafka.hdpt.wx-xdc.group=gameecology_act_${group}

kafka.jiaoyou.sz.consumer.bootstrap-servers=kafkasz004-core001.yy.com:8107,kafkasz004-core002.yy.com:8107,kafkasz004-core003.yy.com:8107
kafka.jiaoyou.sz.consumer.auto-offset-reset=latest

kafka.jiaoyou.wx.consumer.bootstrap-servers=kafkawx006-core001.yy.com:8104,kafkawx006-core002.yy.com:8104,kafkawx006-core003.yy.com:8104
kafka.jiaoyou.wx.consumer.auto-offset-reset=latest

kafka.turnover.consumer.bootstrap-servers=kafkawx012-core001.yy.com:8105,kafkawx012-core002.yy.com:8105,kafkawx012-core003.yy.com:8105
kafka.turnover.consumer.auto-offset-reset=latest

kafka.gamebaby.consumer.bootstrap-servers=kafkawx012-core003.yy.com:8103,kafkawx012-core002.yy.com:8103,kafkawx012-core001.yy.com:8103
kafka.gamebaby.consumer.auto-offset-reset=latest

kafka.hdpt.wx.consumer.bootstrap-servers=kafkawx012-core001.yy.com:8106,kafkawx012-core002.yy.com:8106,kafkawx012-core003.yy.com:8106
kafka.hdpt.wx.consumer.auto-offset-reset=latest

kafka.subchannel.chat.consumer.bootstrap-servers=dmq-wuxi.yy.com:9099
kafka.subchannel.chat.consumer.auto-offset-reset=latest

kafka.hdpt.wx-xdc.consumer.bootstrap-servers=kafkasz012-core001.yy.com:8101,kafkasz012-core002.yy.com:8101,kafkasz012-core003.yy.com:8101
kafka.hdpt.wx-xdc.consumer.auto-offset-reset=latest
skillcard_room_server.s2sname=skillcard_server_yrpc
online.channel.s2s=online_channel_server
currency.s2s=zhuiya_currency_server

kafka.activity.group-id=gameecology_activity_${group}

# yrpc - service discovery
dubbo.consumer.parameters.eagle.appname=${MY_PROJECT_NAME:${eagle_eye_appname}}
dubbo.consumer.parameters.eagle.progress=yrpc-client-prod
dubbo.consumer.parameters.rpclog.thresholdMillis=500
dubbo.consumer.filter=eagle
dubbo.consumer.loadbalance=smart
dubbo.consumer.lbrouters=room,isp

dubbo.provider.parameters.rpclog.thresholdMillis=500
dubbo.provider.parameters.eagle.appname=${MY_PROJECT_NAME:${eagle_eye_appname}}
dubbo.provider.filter=eagle
dubbo.registries.consumer-reg.id=consumer-reg
dubbo.registries.consumer-reg.address=s2s://meta.yy.com
dubbo.registries.consumer-reg.username=ge_access_s2s_acct
dubbo.registries.consumer-reg.password=4b5c7a01baaad935c0f42bdf7d0a1b5c0a2ac7d2850882947e407c3a06c4520124c6ec707edadfe1

# sa
thrift.service-agent.url=nythrift://${MY_HOST_IP}:12500

#xpush
xpush.batch-push-by-uid.uri=https://push-openapi.yy.com/push/batchPushByUid