package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerMp4Config;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.ZhuiwanRoomInfoClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.exception.LotteryException;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.common.bean.CpUid;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.consts.BroadcastType;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.FstAppBroadcastType;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.db.mapper.gameecology.cmpt.Cmpt5150UserPuzzleMapper;
import com.yy.gameecology.common.db.mapper.gameecology.cmpt.Cmpt5150UserPuzzleStateMapper;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2061LatestCp;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5150UserPuzzle;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5150UserPuzzleState;
import com.yy.gameecology.common.locker.Secret;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.PuzzleComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.dao.PuzzleComponentDao;
import com.yy.gameecology.hdzj.utils.BusinessUtils;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.ActorInfoItem;
import com.yy.thrift.hdztranking.ActorQueryItem;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.zhuiwan_room.RoomInfo;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * desc: 拼图玩法
 * fix:250619 - 支持不同等级的奖励
 *
 * <AUTHOR>
 * @date 2025-04-21 14:22
 **/
@Component
@RestController
@RequestMapping("/5150")
public class PuzzleComponent extends BaseActComponent<PuzzleComponentAttr> {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private static final List<RowCol> ALL_ROW_COL = List.of(
            new RowCol(1, 1),
            new RowCol(1, 2),
            new RowCol(1, 3),
            new RowCol(2, 1),
            new RowCol(2, 2),
            new RowCol(2, 3),
            new RowCol(3, 1),
            new RowCol(3, 2),
            new RowCol(3, 3)
    );

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private Cmpt5150UserPuzzleMapper cmpt5150UserPuzzleMapper;

    @Autowired
    private Cmpt5150UserPuzzleStateMapper cmpt5150UserPuzzleStateMapper;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private LatestCpComponent latestCpComponent;

    @Autowired
    private ChannelWatchwordLotteryComponent channelWatchwordLotteryComponent;

    @Autowired
    private PuzzleComponentDao puzzleComponentDao;

    @Autowired
    private ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;

    private static final String AWARD_STAT = "award_stat";

    private final static String CP_REWARD_LIMIT = "cp_reward_limit";

    private static final String INFOFLOW_MSG_TEMPLATE = """
            单个奖品统计：
            | 奖品名称 | 发放数量 |
            | ------ | ------- |
            <#list awardList as award>
            | ${award.awardDetail} | ${award.awardCount?c} |
            </#list>
            
            累计发放金额（单位：厘）：${totalAmount}
            """;
    private static final freemarker.template.Template TEMPLATE;

    static {
        try {
            TEMPLATE = new freemarker.template.Template("infoflow_5150", INFOFLOW_MSG_TEMPLATE, null);
        } catch (IOException e) {
            throw new IllegalStateException(e);
        }
    }

    private final static long BRO_BANNER_ID = 5150001;
    @Autowired
    private DelaySvcSDKServiceV2 delaySvcSDKServiceV2;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private ThreadPoolManager threadPoolManager;


    @Override
    public Long getComponentId() {
        return ComponentId.PUZZLE;
    }

    @NeedRecycle(author = "liqingyang", notRecycle = true)
    @Scheduled(cron = "38 0 * * * *")
    public void sendTaskStats() {
        timerSupport.work("sendTaskStats:" + ComponentId.PUZZLE, 10, () -> {
            Set<Long> actIds = this.getComponentEffectActIds();
            if (CollectionUtils.isEmpty(actIds)) {
                return;
            }
            for (Long actId : actIds) {
                if (!actInfoService.inActShowTime(actId)) {
                    continue;
                }

                var attr = tryGetUniqueComponentAttr(actId);
                sendStatInfoflowNotice(attr);
            }
        });
    }

    /**
     * cp过任务，增加碎片余额、连线发奖、集齐重置+发奖
     */
    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
    public void onTaskProgressChanged(TaskProgressChanged event, PuzzleComponentAttr attr) {
        log.info("onTaskProgressChanged start seq:{}, event:{}", event.getSeq(), JSON.toJSONString(event));

        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }
        Secret secret = null;
        String name = makeKey(attr, "dealTaskProgressChange:" + event.getMember());
        try {
            secret = locker.lock(name, 10, "", 10);
            if (secret == null) {
                throw new RuntimeException("onTaskProgressChanged lock fail, to retry");
            }
            dealTaskProgressChange(event, attr);
        } catch (Exception e) {
            log.error("onTaskProgressChanged error,actId:{},e:{}", event.getActId(), e.getMessage(), e);
            throw e;

        } finally {
            if (secret != null) {
                locker.unlock(name, secret);
            }
        }
    }

    @RequestMapping("/getCpMissions")
    public Response<CpMission> getCpMissions(Long actId, Long cmptInx, String cpMember) {
        long uid = getLoginYYUid();
        if (SysEvHelper.isHistory()) {
            return Response.fail(400, "活动已结束!");
        }

        PuzzleComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(404, "活动未配置");
        }
        CpMission cpMission = new CpMission();
        CpUid cpUid = null;
        if (StringUtil.isNotBlank(cpMember)) {
            cpUid = Const.splitCpMember(cpMember);
        }

        //分数
        long score = 0;
        if (cpUid != null) {
            ActorQueryItem queryItem = new ActorQueryItem();
            queryItem.setActorId(cpMember);
            queryItem.setRankingId(attr.getRankId());
            queryItem.setPhaseId(attr.getPhaseId());
            queryItem.setWithStatus(false);
            String dateCode = TimeKeyHelper.getTimeCode(attr.getTimeKey(), commonService.getNow(actId));
            queryItem.setDateStr(dateCode);
            ActorInfoItem actorInfoItem = hdztRankingThriftClient.queryActorRankingInfo(actId, queryItem);
            if (actorInfoItem != null) {
                score = actorInfoItem.getScore();
                cpMission.setScore(score);
            }
        }

        //用户信息
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(1);
        if (uid > 0) {
            var userInfoMap = userInfoService.getUserInfoWithNickExt(List.of(uid), multiNickUsers);
            var userInfo = userInfoMap.get(uid);
            if (userInfo != null) {
                cpMission.setAvatar(userInfo.getAvatarUrl());
                cpMission.setNick(userInfo.getNick());
            }
        }

        int round = 0;

        if (cpUid != null) {
            var puzzleState = cmpt5150UserPuzzleStateMapper.select(actId, cmptInx, cpUid.getUserUid(), cpUid.getAnchorUid());
            round = puzzleState == null ? 0 : puzzleState.getRound();
        }

        final int level = round % attr.getLevelMod();
        cpMission.setRound(round);
        cpMission.setLevel(level);

        //奖励配置
        Map<Integer, Map<String, Award>> allAwards = new HashMap<>(attr.getSomePartsAward().size());
        for (var entry : attr.getSomePartsAward().entrySet()) {
            int lv = entry.getKey();
            Map<String, AwardAttrConfig> somePartsAward = entry.getValue();
            Map<String, Award> awardMap = new HashMap<>(somePartsAward.size());
            for (var subEntry : somePartsAward.entrySet()) {
                String key = subEntry.getKey();
                var awardConfig = subEntry.getValue();
                var awardModel = hdztAwardServiceClient.queryAwardTask(awardConfig.getTAwardTskId(), awardConfig.getTAwardPkgId());
                Award award = new Award();
                if (awardModel != null) {
                    award.setName(awardModel.getPackageName());
                    award.setPic(awardModel.getPackageImage());
                }
                awardMap.put(key, award);
            }

            allAwards.put(lv, awardMap);
        }
        cpMission.setAllAwards(allAwards);

        //任务进度
        List<Mission> missions = new ArrayList<>(attr.getMissions().size());
        for (int i = 0; i < attr.getMissions().size(); i++) {
            long missionVal = attr.getMissions().get(i);
            Mission mission = new Mission();
            mission.setTaskId(i + 1);
            mission.setScore(missionVal);
            mission.setFinish(missionVal <= score);
            missions.add(mission);
        }
        cpMission.setMissions(missions);

        //图片是否点亮
        if (cpUid != null) {
            List<Cmpt5150UserPuzzle> puzzles = cmpt5150UserPuzzleMapper.selectPuzzle(actId, cmptInx, cpUid.getUserUid(), cpUid.getAnchorUid());
            List<RowCol> rowCols = Lists.newArrayList();
            for (Cmpt5150UserPuzzle puzzle : puzzles) {
                if (puzzle.getBalance() <= 0) {
                    continue;
                }
                RowCol rowCol = convertToRowCol(Convert.toInt(puzzle.getPuzzleCode()));
                rowCols.add(rowCol);
            }
            cpMission.setRowColLight(rowCols);

            Map<Integer, List<RowCol>> allRowColsLight = new HashMap<>(attr.getLevelMod());
            for (int i = 0; i < attr.getLevelMod(); i++) {
                if (i == level) {
                    allRowColsLight.put(i, new ArrayList<>(rowCols));
                } else if (i < round) {
                    allRowColsLight.put(i, new ArrayList<>(ALL_ROW_COL));
                }
            }

            cpMission.setAllRowColsLight(allRowColsLight);
        }

        return Response.success(cpMission, multiNickUsers);
    }

    @RequestMapping("/awardLimitInfo")
    public Response<LimitInfo> awardLeft(long actId,
                                         @RequestParam(name = "cmptInx", required = false, defaultValue = "810") long cmptInx) {
        PuzzleComponentAttr attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(-1, "活动未配置");
        }
        LimitInfo info = new LimitInfo();
        info.setTotal(attr.getCpAwardLimit());
        long nowAmount = getAwardPoolLeft(attr);
        info.setLeft(attr.getCpAwardLimit() - nowAmount > 0 ? attr.getCpAwardLimit() - nowAmount : 0);
        return Response.success(info);
    }

    private long getAwardPoolLeft(PuzzleComponentAttr attr) {
        return commonDataDao.valueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), CP_REWARD_LIMIT);
    }

    @RequestMapping("/testBanner")
    public Response<String> testBanner(String seq, long actId, long cmptInx, long userUid, long anchorUid, long sid, long ssid) {
        if (!SysEvHelper.isDev()) {
            return Response.fail(-1, "not test env");
        }
        log.info("testBanner test");
        var attr = getComponentAttr(actId, cmptInx);
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(2);
        Map<Long, UserInfoVo> userInfos = userInfoService.getCpUserInfoWithNickExt(userUid, anchorUid, multiNickUsers);
        collectAllAwardBro(seq, attr, userUid, anchorUid, 0, attr.getCollectAllAward().get(0).get(1), Pair.of(sid, ssid), userInfos, multiNickUsers);
        return Response.success(seq);

    }

    @RequestMapping("/testTips")
    public Response<String> testTips(String seq, long actId, long cmptInx, long userUid, long anchorUid) {
        if (!SysEvHelper.isDev()) {
            return Response.fail(-1, "not test env");
        }
        var attr = getComponentAttr(actId, cmptInx);
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(2);
        Map<Long, UserInfoVo> userInfos = userInfoService.getCpUserInfoWithNickExt(userUid, anchorUid, multiNickUsers);
        CpUid cpUid = new CpUid();
        cpUid.setUserUid(userUid);
        cpUid.setAnchorUid(anchorUid);
        somePartsAwardNotice(attr, seq, 0, "1", 1000, cpUid, userInfos, 0);
        return Response.success(seq);
    }

    @GetMapping("testStat")
    public Response<?> testStat(long actId, long cmptInx) {
        if (!SysEvHelper.isDev()) {
            return Response.fail(-1, "not test env");
        }

        var attr = getComponentAttr(actId, cmptInx);
        sendStatInfoflowNotice(attr);

        return Response.ok();
    }

    private void dealTaskProgressChange(TaskProgressChanged event, PuzzleComponentAttr attr) {
        CpUid cpUid = Const.splitCpMember(event.getMember());
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(2);
        Map<Long, UserInfoVo> userInfos = userInfoService.getCpUserInfoWithNickExt(cpUid.getUserUid(), cpUid.getAnchorUid(), multiNickUsers);
        final String eventSeq = StringUtil.isNotBlank(event.getEkey()) ? event.getEkey() : event.getSeq();

        Date endTime = DateUtil.getDate(event.getOccurTime());
        String dateCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), endTime);

        var channelInfo = getAnchorLastChannel(attr, event, cpUid.getAnchorUid(), dateCode);
        Map<Integer, List<String>> userPieceCodeMap = new HashMap<>(attr.getLevelMod());
        for (long taskIndex = event.getStartTaskIndex() + 1; taskIndex <= event.getCurrTaskIndex(); taskIndex++) {
            String seq = eventSeq + ":" + taskIndex;

            log.info("dealTaskProgressChange,actId:{},member:{},index:{},seq:{}", event.getActId(), event.getMember(), taskIndex, seq);

            //集齐发奖(为防止后置清空失败中断，先尝试前置清空)
            Pair<Integer, AwardAttrConfig> awardResult = collectAllAward(attr, cpUid, event.getMember(), channelInfo, dateCode);
            // 当前的轮次
            final int currentRound = awardResult.getRight() == null ? awardResult.getLeft() : awardResult.getLeft() + 1;

            //发碎片
            String userPieceCode = onTaskProgressAwardPiece(seq, event, taskIndex, cpUid, attr);
            userPieceCodeMap.computeIfAbsent(currentRound, k -> new ArrayList<>(attr.getTAwardPkgPieces().size())).add(userPieceCode);
//            userPieceCodes.add(userPieceCode);

            //连线发奖
            somePartsAward(seq, event, cpUid, attr);

            //集齐发奖
            awardResult = collectAllAward(attr, cpUid, event.getMember(), channelInfo, dateCode);

            //集齐所有全服广播
            if (awardResult.getRight() != null) {
                var round = awardResult.getLeft();
                var awardAttrConfig = awardResult.getRight();
                log.info("dealTaskProgressChange done with round:{},awardAttrConfig:{}", round, awardAttrConfig);
                collectAllAwardBro(seq, attr, cpUid.getUserUid(), cpUid.getAnchorUid(), round, awardAttrConfig, channelInfo, userInfos, multiNickUsers);
            }
        }

        //连线奖励单播合并提醒
        long delay = 8;
        for (var entry : userPieceCodeMap.entrySet()) {
            String pieceContent = StringUtils.join(entry.getValue(), ',');
            somePartsAwardNotice(attr, eventSeq + entry.getKey(), entry.getKey(), pieceContent, event.getRankScore(), cpUid, userInfos, delay);
            delay += 5;
        }
    }

    private String onTaskProgressAwardPiece(String seq, TaskProgressChanged event, long taskIndex, CpUid cpUid, PuzzleComponentAttr attr) {
        long awardTaskId;
        long userAwardPackageId;
        final String userPieceCode;

        boolean sendNotObtained = attr.getSendNotObtainedTaskLevel().contains(taskIndex);
        if (sendNotObtained) {
            var userAward = tryGetUserNotObtainedAward(attr, cpUid);
            //发之前、发完后尝试集满清空结算，这里不允许存在已经集满的中间状态
            if (userAward == null) {
                throw new RuntimeException(String.format("tryGetUserNotObtainedAward null,actId:%s,uid:%s", attr.getActId(), event.getMember()));
            }
            awardTaskId = userAward.getTAwardTskId();
            userAwardPackageId = userAward.getTAwardPkgId();
            userPieceCode = userAward.getPieceCode();

            log.info("onTaskProgressAwardPiece sendNotObtained,actId:{},taskIndex:{},cp:{},userPieceCode:{}", attr.getActId(), event.getMember(), taskIndex, userPieceCode);

        } else {

            String lotterySeq = MD5SHAUtil.getMD5("lottteryPiece_" + seq);
            Map<String, String> extData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(cpUid.getUserUid()));
            BatchLotteryResult result = hdztAwardServiceClient.doLottery(DateUtil.getNowYyyyMMddHHmmss(),
                    BusiId.GAME_ECOLOGY.getValue(), cpUid.getAnchorUid(), attr.getPieceLotteryTaskId(), 1, 0, lotterySeq, extData);
            log.info("uid:{} lottery result:{}", cpUid.getAnchorUid(), JsonUtil.toJson(result));
            //SEQ 重复的code==6000
            if (result == null) {
                log.error("doLottery fail result is null, uid:{},seq:{}", cpUid.getAnchorUid(), lotterySeq);
                throw new RuntimeException("doLottery error");
            }

            if (result.getCode() != 0 && result.getCode() != LotteryException.E_LOTTERY_COMPLETED) {
                log.error("doLottery error,uid:{},seq:{},result:{}", cpUid.getAnchorUid(), lotterySeq, JSON.toJSONString(result));
                throw new RuntimeException("doLottery error");
            }
            long packageId = result.getRecordIds().keySet().iterator().next();
            PuzzleComponentAttr.PieceAward pieceAward = attr.getTAwardPkgPieces().get(packageId);

            awardTaskId = pieceAward.getTAwardTskId();
            userAwardPackageId = pieceAward.getTAwardPkgId();
            userPieceCode = pieceAward.getPieceCode();
        }


        //增加碎片状态数据
        puzzleComponentDao.addPuzzleBalance(seq, event, cpUid, attr, userPieceCode);

        //---发奖系统增加碎片流水
        //用户
        String userAwardSeq = MD5SHAUtil.getMD5("award_piece_user_" + seq);
        Map<String, String> extData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(cpUid.getAnchorUid()));
        BatchWelfareResult result = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), cpUid.getUserUid(), awardTaskId, 1, userAwardPackageId, userAwardSeq, extData, 2);
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("welfare exception");
        }
        if (isNotTheSameMember(cpUid)) {
            //主播
            String anchorAwardSeq = MD5SHAUtil.getMD5("award_piece_anchor_" + seq);
            Map<String, String> anchorExtData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(cpUid.getUserUid()));
            BatchWelfareResult anchorResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), cpUid.getAnchorUid(), awardTaskId, 1, userAwardPackageId, anchorAwardSeq, anchorExtData, 2);
            if (anchorResult == null || anchorResult.getCode() != 0) {
                throw new RuntimeException("welfare exception");
            }
        }

        return userPieceCode;
    }

    private PuzzleComponentAttr.PieceAward tryGetUserNotObtainedAward(PuzzleComponentAttr attr, CpUid cpUid) {
        List<Cmpt5150UserPuzzle> userPuzzles = cmpt5150UserPuzzleMapper.selectPuzzle(attr.getActId(), attr.getCmptUseInx(), cpUid.getUserUid(), cpUid.getAnchorUid());
        Set<String> userPuzzleCode = userPuzzles
                .stream()
                .filter(p -> p.getBalance() > 0)
                .map(Cmpt5150UserPuzzle::getPuzzleCode).collect(Collectors.toSet());

        List<PuzzleComponentAttr.PieceAward> pieceAwards = Lists.newArrayList(attr.getTAwardPkgPieces().values());
        Collections.shuffle(pieceAwards);
        Optional<PuzzleComponentAttr.PieceAward> notObtained = pieceAwards.stream()
                .filter(p -> !userPuzzleCode.contains(p.getPieceCode()))
                .findFirst();


        if (notObtained.isPresent()) {
            return notObtained.get();
        } else {
            log.warn("tryGetUserNotObtainedAward failed,user already get all puzzle? actId:{}, cpUid:{}", attr.getActId(), JSON.toJSONString(cpUid));
            return null;
        }
    }

    private List<AwardAttrConfig> somePartsAward(String seq, TaskProgressChanged event, CpUid cpUid, PuzzleComponentAttr attr) {
        List<AwardAttrConfig> awardResult = Lists.newArrayList();

        List<Cmpt5150UserPuzzle> userPuzzles = cmpt5150UserPuzzleMapper.selectPuzzle(attr.getActId(), attr.getCmptUseInx(), cpUid.getUserUid(), cpUid.getAnchorUid());
        Cmpt5150UserPuzzleState state = cmpt5150UserPuzzleStateMapper.select(attr.getActId(), attr.getCmptUseInx(), cpUid.getUserUid(), cpUid.getAnchorUid());
        int round = state == null ? 0 : state.getRound();
        final int level = round % attr.getLevelMod();

        Map<String, AwardAttrConfig> somePartsAward = attr.getSomePartsAward().getOrDefault(level, Collections.emptyMap());

        Set<String> userPuzzleCode = userPuzzles
                .stream()
                .filter(p -> p.getBalance() > 0)
                .map(Cmpt5150UserPuzzle::getPuzzleCode).collect(Collectors.toSet());
        for (var entry : somePartsAward.entrySet()) {
            String codes = entry.getKey();
            List<String> awardCode = Arrays.stream(codes.split(",")).toList();
            boolean needAward = userPuzzleCode.containsAll(awardCode);
            if (!needAward) {
                continue;
            }
            //部分集齐发奖
            AwardAttrConfig awardAttrConfig = entry.getValue();

            //发奖接口seq有兜底去重，因此这里可以先读后写
            String dupAwardKey = event.getMember() + "_" + codes + "_" + round;
            String alreadyAward = commonDataDao.hashValueGet(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), "somePartsAward", dupAwardKey);
            if (StringUtil.isNotBlank(alreadyAward)) {
                log.warn("already award,skip,dupAwardKey:{}", dupAwardKey);
                continue;
            }

            log.info("somePartsAward begin,actId:{}, award:{},cpUid:{}", attr.getActId(), JSON.toJSON(awardAttrConfig), JSON.toJSONString(cpUid));

            //主播
            String anchorAwardSeq = Const.addActivityPrefix(event.getActId(), "somePartsAwardAnchor:" + event.getMember() + ":" + cpUid.getAnchorUid() + ":" + codes + ":" + round);
            final String anchorAwardSeqMd5 = MD5SHAUtil.getMD5(anchorAwardSeq);
            Map<String, String> anchorExtData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(cpUid.getUserUid()),
                                                       HdztAwardServiceClient.DUP_SHOW_SEQ, anchorAwardSeqMd5,
                                                       HdztAwardServiceClient.CP_MEMBER, cpUid.getMember());
            BatchWelfareResult anchorResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), cpUid.getAnchorUid(), awardAttrConfig.getTAwardTskId(), awardAttrConfig.getNum(), awardAttrConfig.getTAwardPkgId(), anchorAwardSeqMd5, anchorExtData, 2);
            if (anchorResult == null || anchorResult.getCode() != 0) {
                throw new RuntimeException("welfare exception");
            }
            log.info("anchor award done,seq:{},md5Seq:{},uid:{},packageId:{},result:{}", anchorAwardSeq, anchorAwardSeqMd5, cpUid.getAnchorUid(), awardAttrConfig.getTAwardPkgId(), anchorResult);

            if (isNotTheSameMember(cpUid)) {
                //用户
                String userAwardSeq = Const.addActivityPrefix(event.getActId(), "somePartsAwardUser:" + event.getMember() + ":" + cpUid.getUserUid() + ":" + codes + ":" + round);
                String userAwardSeqMd5 = MD5SHAUtil.getMD5(userAwardSeq);
                Map<String, String> userExtData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(cpUid.getAnchorUid()),
                                                         HdztAwardServiceClient.DUP_SHOW_SEQ, anchorAwardSeqMd5,
                                                         HdztAwardServiceClient.CP_MEMBER, cpUid.getMember());
                BatchWelfareResult userResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), cpUid.getUserUid(), awardAttrConfig.getTAwardTskId(), awardAttrConfig.getNum(), awardAttrConfig.getTAwardPkgId(), userAwardSeqMd5, userExtData, 2);
                if (userResult == null || userResult.getCode() != 0) {
                    throw new RuntimeException("welfare exception");
                }
                log.info("user award done,seq:{},md5Seq:{},uid:{},packageId:{},result:{}", userAwardSeq, userAwardSeqMd5, cpUid.getUserUid(), awardAttrConfig.getTAwardPkgId(), userResult);
            }


            boolean award = commonDataDao.hashValueSetNX(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), "somePartsAward", dupAwardKey, DateUtil.format(new Date()));
            if (award) {
                awardResult.add(awardAttrConfig);
                // add static
                threadPoolManager.get(Const.GENERAL_POOL).execute(() -> addStat(attr, dupAwardKey, awardAttrConfig));
            }
        }

        return awardResult;
    }

    /**
     *
     * @param attr
     * @param cpUid
     * @param memberId
     * @param dateCode
     * @return left -> 当前的round， right -> 奖励对象，null为未集齐拼图碎片
     */
    @NotNull
    private Pair<Integer, AwardAttrConfig> collectAllAward(PuzzleComponentAttr attr, CpUid cpUid, String memberId, Pair<Long, Long> channelInfo, String dateCode) {
        List<Cmpt5150UserPuzzle> userPuzzles = cmpt5150UserPuzzleMapper.selectPuzzle(attr.getActId(), attr.getCmptUseInx(), cpUid.getUserUid(), cpUid.getAnchorUid());

        Cmpt5150UserPuzzleState state = cmpt5150UserPuzzleStateMapper.select(attr.getActId(), attr.getCmptUseInx(), cpUid.getUserUid(), cpUid.getAnchorUid());
        final int round = state == null ? 0 : state.getRound();
        final int level = round % attr.getLevelMod();

        Set<String> userPuzzleCode = userPuzzles
                .stream()
                .filter(p -> p.getBalance() > 0)
                .map(Cmpt5150UserPuzzle::getPuzzleCode).collect(Collectors.toSet());
        boolean collectAll = userPuzzleCode.containsAll(attr.getAllPieceCodes());
        if (!collectAll) {
            return Pair.of(round, null);
        }

        String reducePoolSeq = Const.addActivityPrefix(attr.getActId(), "collectAllAwardPool:" + memberId + ":" + round);
        CommonDataDao.LimitUpdateState limitInfo = commonDataDao.valueGetSnapshot(attr.getActId(), attr.getCmptId(), (int) attr.getCmptUseInx(), CP_REWARD_LIMIT, reducePoolSeq);
        final int awardConfigKey = 1;
        AwardAttrConfig awardConfig = attr.getCollectAllAward().getOrDefault(level, Collections.emptyMap()).get(awardConfigKey);

        UpdatePoolStatus statusRet = getPoolStatus(attr, limitInfo, awardConfig);

        if (statusRet.getIsOver()) {
            log.info("collectAllAward award pool out release,uid:{},anchorUid:{},awardConfig:{}", cpUid.getUserUid(), cpUid.getAnchorUid(), JSON.toJSONString(awardConfig));
            final int poolOutAwardConfigKey = -1;
            awardConfig = attr.getCollectAllAward().getOrDefault(level, Collections.emptyMap()).get(poolOutAwardConfigKey);
        }

        if (!statusRet.getUpdated()) {
            long limitCost = isNotTheSameMember(cpUid) ? awardConfig.getAwardAmount() * 2 : awardConfig.getAwardAmount();
            commonDataDao.valueIncrIgnore(attr.getActId(), attr.getCmptId(), (int) attr.getCmptUseInx(), reducePoolSeq, CP_REWARD_LIMIT, limitCost);
        }

        log.info("collectAllAward,actId:{},member:{},packageId:{}", attr.getActId(), memberId, awardConfig.getTAwardPkgId());

        //主播
        String anchorAwardSeq = Const.addActivityPrefix(attr.getActId(), "collectAllAwardAnchor:" + memberId + ":" + cpUid.getAnchorUid() + ":" + round);
        String anchorAwardSeqMd5 = MD5SHAUtil.getMD5(anchorAwardSeq);
        String viewExt = "{\"level\":"+level+"}";
        Map<String, String> anchorExtData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(cpUid.getUserUid()), HdztAwardServiceClient.DUP_SHOW_SEQ, anchorAwardSeqMd5, HdztAwardServiceClient.CP_MEMBER, cpUid.getMember(), HdztAwardServiceClient.VIEW_EXT, viewExt);
        BatchWelfareResult anchorResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), cpUid.getAnchorUid(), awardConfig.getTAwardTskId(), awardConfig.getNum(), awardConfig.getTAwardPkgId(), anchorAwardSeqMd5, anchorExtData, 2);
        //seq重复code也是==0
        if (anchorResult == null || anchorResult.getCode() != 0) {
            throw new RuntimeException("welfare exception");
        }
        log.info("anchor collectAllAward done,uid:{},packageId:{},seq:{},md5Seq:{},ret:{}", cpUid.getAnchorUid(), awardConfig.getTAwardPkgId(), anchorAwardSeq, anchorAwardSeqMd5, anchorResult);

        //用户
        if (isNotTheSameMember(cpUid)) {
            String userAwardSeq = Const.addActivityPrefix(attr.getActId(), "collectAllAwardUser:" + memberId + ":" + cpUid.getUserUid() + ":" + round);
            String userAwardSeqMd5 = MD5SHAUtil.getMD5(userAwardSeq);
            Map<String, String> userExtData = Map.of(HdztAwardServiceClient.CP_UID, String.valueOf(cpUid.getAnchorUid()), HdztAwardServiceClient.DUP_SHOW_SEQ, anchorAwardSeqMd5, HdztAwardServiceClient.CP_MEMBER, cpUid.getMember(), HdztAwardServiceClient.VIEW_EXT, viewExt);
            BatchWelfareResult userResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), attr.getBusiId(), cpUid.getUserUid(), awardConfig.getTAwardTskId(), awardConfig.getNum(), awardConfig.getTAwardPkgId(), userAwardSeqMd5, userExtData, 2);
            if (userResult == null || userResult.getCode() != 0) {
                throw new RuntimeException("welfare exception");
            }
            log.info("user collectAllAward done,uid:{},packageId:{},seq:{},md5Seq:{},ret:{}", cpUid.getUserUid(), awardConfig.getTAwardPkgId(), userAwardSeq, userAwardSeqMd5, userResult);
        }

        puzzleComponentDao.clearPuzzle(cpUid, attr, 1);

        // 口令抽奖
        Long watchwordLotteryIndex = attr.getWatchwordLotteryIndexes().get(level);
        if (watchwordLotteryIndex != null) {
            if (channelInfo != null) {
                String seq = String.format("puzzle:%s:%d", memberId, round);
                Date now = commonService.getNow(attr.getActId());
                Date expiredTime = DateUtils.addSeconds(now, (int) attr.getWatchwordLotteryDuration().toSeconds());
                channelWatchwordLotteryComponent.addWatchwordLotteryBox(attr.getActId(), watchwordLotteryIndex, seq, memberId, channelInfo.getLeft(), channelInfo.getRight(), expiredTime);
            } else {
                log.error("trying to add watchword lottery last channel is null memberId:{}", memberId);
            }
        }

        // add stat
        final var awardAttrConfig = awardConfig;
        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> addStat(attr, reducePoolSeq, awardAttrConfig));

        return Pair.of(round, awardConfig);
    }

    private UpdatePoolStatus getPoolStatus(PuzzleComponentAttr attr, CommonDataDao.LimitUpdateState limitInfo, AwardAttrConfig awardConfig) {
        UpdatePoolStatus ret = new UpdatePoolStatus(false, false);
        // 更新过
        if (limitInfo.getUpdateSate() > 0) {
            ret.setUpdated(true);
            ret.setAfterAmount(limitInfo.getAfterValue());
            ret.setBeforeAmount(limitInfo.getBeforeValue());
            if (ret.getBeforeAmount() >= attr.getCpAwardLimit()) {
                ret.setIsOver(true);
            }
        }
        //  没有更新过
        else {
            ret.setBeforeAmount(limitInfo.getBeforeValue());
            if (ret.getBeforeAmount() < attr.getCpAwardLimit()) {
                ret.setAfterAmount(ret.getBeforeAmount() + awardConfig.getAwardAmount() * 2);
            } else {
                ret.setIsOver(true);
                ret.setAfterAmount(ret.getBeforeAmount());
            }
        }
        return ret;
    }

    // mp4 特效
    private void collectAllAwardBro(String seq, PuzzleComponentAttr attr, long bossUid, long anchorUid, int round, AwardAttrConfig awardConfig, Pair<Long, Long> channelInfo, Map<Long, UserInfoVo> userInfos, Map<String, Map<String, MultiNickItem>> multiNickUsers) {
        log.info("collectAllAwardBro bossUid:{} anchorUid:{} seq:{}", bossUid, anchorUid, seq);
        //---pc 广播横幅[全频道广播]
        Map<String, Object> ext = new HashMap<>(32);
        UserInfoVo userInfoVo = userInfos.get(bossUid);
        UserInfoVo babyInfoVo = userInfos.get(anchorUid);
        final int level = round % attr.getLevelMod();

        ext.put("round", round);
        ext.put("level", level);
        ext.put("userUid", bossUid);
        ext.put("babyUid", anchorUid);
        long sid = 0, ssid = 0;
        if (channelInfo != null) {
            sid = channelInfo.getLeft();
            ssid = channelInfo.getRight();
        }
        ext.put("sid", sid);
        ext.put("ssid", ssid);
        if (userInfoVo != null) {
            ext.put("userLogo", userInfoVo.getAvatarUrl());
            ext.put("userNick", userInfoVo.getNick());
        }
        if (babyInfoVo != null) {
            ext.put("babyLogo", babyInfoVo.getAvatarUrl());
            ext.put("babyNick", babyInfoVo.getNick());
        }
        ext.put("nickExtUsers", JsonUtil.toJson(multiNickUsers));

        var awardModel = hdztAwardServiceClient.queryAwardTask(awardConfig.getTAwardTskId(), awardConfig.getTAwardPkgId());

        ext.put("giftName", awardModel.getPackageName());
        int count = HdztAwardServiceClient.getAwardCount(awardModel, awardConfig.getNum());
        ext.put("giftCount", count);
        ext.put("giftIcon", awardModel.getPackageImage());
        ext.put("giftUnit", awardModel.getUnit());
        if (StringUtil.isNotBlank(awardConfig.getExtJson())) {
            JSONObject jsonObject = JSON.parseObject(awardConfig.getExtJson());
            ext.put("giftCount", jsonObject.getIntValue("count"));
        }

        String broSeq = makeKey(attr, "collectAllAwardBro:" + seq);
        Template template = BusinessUtils.getTemplateByBusiId(attr.getBusiId());
        long broSid = sid, broSsid = ssid;
        RetryTool.withRetryCheck(attr.getActId(), broSeq, () -> {
            if (attr.isFilterDanmaku()) {
                log.info("collectAllAwardBro filter danmaku bossUid:{} anchorUid:{} seq:{}", bossUid, anchorUid, seq);
                commonBroadCastService.commonBannerBroadcastAllTemplateExcludeDanmu(template, attr.getActId(), 0, 0, BRO_BANNER_ID, 0, ext);
            } else {
                final long familyId;
                if (attr.getBroType() == BroadcastType.FAMILY) {
                    RoomInfo roomInfo = zhuiwanRoomInfoClient.roomInfoBySsid(broSsid);
                    familyId = roomInfo.getFamilyId();
                } else {
                    familyId = 0;
                }

                GameecologyActivity.BannerBroadcast.Builder bannerBroadcast = GameecologyActivity.BannerBroadcast.newBuilder()
                        .setActId(attr.getActId())
                        .setBannerId(BRO_BANNER_ID)
                        .setAnchorUid(anchorUid)
                        .setBannerType(0)
                        .setJsonData(JSON.toJSONString(ext));


                GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                        .setUri(GameecologyActivity.PacketType.BannerBroadcastUri_VALUE)
                        .setBannerBroadcast(bannerBroadcast).build();

                broadCastHelpService.broadcast(broSeq, attr.getActId(), BusiId.findByValue(attr.getBusiId()), attr.getBroType(), familyId, broSid, broSsid, msg);
            }
        });


        //---app mp4 特效
        String mp4Url = attr.getMp4Urls().get(level);
        if (StringUtils.isEmpty(mp4Url)) {
            return;
        }
        String appBannerSeq = makeKey(attr, "collectAllAwardAppBanner:" + seq);
        AppBannerMp4Config mp4Config = new AppBannerMp4Config();
        List<Map<String, String>> broContentLayers = getMp4TextConfig(attr, bossUid, anchorUid, userInfos, awardConfig);
        mp4Config.setLayerExtKeyValues(broContentLayers);
        mp4Config.setUrl(mp4Url);
        mp4Config.setLevel(attr.getMp4Level());

        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), appBannerSeq,
                BroadCastHelpService.toAppBroBusiness(attr.getBusiId()), FstAppBroadcastType.ALL_TEMPLATE, sid, ssid, "", Lists.newArrayList());
        appBannerEvent.setMp4Config(mp4Config);
        final int mp4ContentType = 5;
        appBannerEvent.setContentType(mp4ContentType);
        appBannerEvent.setAppId(commonService.getTurnoverAppId(attr.getBusiId()));
        appBannerEvent.setUidList(Lists.newArrayList(bossUid, anchorUid));


        log.info("appBannerEvent:{}", appBannerEvent);
        RetryTool.withRetryCheck(attr.getActId(), appBannerSeq, () -> {
            if (attr.isFilterDanmaku()) {
                log.info("sendAppBannerKafkaExcludeDanmuku");
                kafkaService.sendAppBannerKafkaExcludeDanmuku(appBannerEvent);
            } else {
                appBannerEvent.setBcType(getFtsBcTypeByBroType(attr.getBroType()));
                kafkaService.sendAppBannerKafka(appBannerEvent);
            }
        });
    }

    private List<Map<String, String>> getMp4TextConfig(PuzzleComponentAttr attr, long userUid, long anchorUid, Map<Long, UserInfoVo> userInfos, AwardAttrConfig awardConfig) {
        List<Map<String, String>> result = Lists.newArrayList();
        var awardModel = hdztAwardServiceClient.queryAwardTask(awardConfig.getTAwardTskId(), awardConfig.getTAwardPkgId());
        for (String key : attr.getMp4LayerExtKeyValues().keySet()) {
            Map<String, String> keyMap = Maps.newHashMap();
            String text = attr.getMp4LayerExtKeyValues().get(key);
            if (StringUtils.isNotBlank(text)) {
                //替换uid
                text = text.replace("{userNick}", "{" + userUid + ":n}");
                text = text.replace("{anchorNick}", "{" + anchorUid + ":n}");

                UserInfoVo userInfoVo = userInfos.get(userUid);
                if (userInfoVo != null) {
                    text = text.replace("{userLogo}", userInfoVo.getAvatarUrl());
                }
                UserInfoVo babyInfoVo = userInfos.get(anchorUid);
                if (babyInfoVo != null) {
                    text = text.replace("{anchorLogo}", babyInfoVo.getAvatarUrl());
                }
                //{giftName}{giftNum}{giftUnit}
                text = text.replace("{giftName}", awardModel.getPackageName());
                text = text.replace("{giftIcon}", awardModel.getPackageImage());
                text = text.replace("{giftUnit}", awardModel.getUnit());
                if (StringUtil.isNotBlank(awardConfig.getExtJson())) {
                    JSONObject jsonObject = JSON.parseObject(awardConfig.getExtJson());
                    text = text.replace("{giftCount}", jsonObject.getString("count"));
                }
            }

            keyMap.put(key, text);
            result.add(keyMap);
        }
        return result;
    }

    /**
     *
     * @param attr
     * @param seq
     * @param round
     * @param pieceCode
     * @param score
     * @param cpUid
     * @param userInfos
     * @param delay 延迟：单位秒
     */
    private void somePartsAwardNotice(PuzzleComponentAttr attr, String seq, int round, String pieceCode, long score, CpUid cpUid, Map<Long, UserInfoVo> userInfos, long delay) {
        Map<String, Object> noticeExt = new HashMap<>(9);
        noticeExt.put("round", round);
        noticeExt.put("level", round % attr.getLevelMod());
        noticeExt.put("pieceCode", pieceCode);
        noticeExt.put("score", score);
        noticeExt.put("cpMember", cpUid.getMember());
        String noticeSeq = makeKey(attr, "somePartsAwardNotice:" + seq);
        RetryTool.withRetryCheck(attr.getActId(), noticeSeq, () -> {

            UserInfoVo userInfo = userInfos.get(cpUid.getUserUid());
            if (userInfo != null) {
                noticeExt.put("cpNick", userInfo.getNick());
            }
            boolean anchorSkipNotice = attr.isFilterDanmaku() && commonService.inDanmuChannel(cpUid.getAnchorUid());
            if (!anchorSkipNotice) {
                GameecologyActivity.CommonNoticeResponse.Builder tips = GameecologyActivity.CommonNoticeResponse.newBuilder()
                        .setActId(attr.getActId())
                        .setNoticeType(getComponentId() + "_some_parts_award_notice")
                        .setNoticeValue(JSON.toJSONString(noticeExt))
                        .setExtJson(StringUtils.EMPTY);

                GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                        .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                        .setCommonNoticeResponse(tips).build();
                if (delay > 0) {
                    delaySvcSDKServiceV2.unicastUid(cpUid.getAnchorUid(), msg, delay);
                } else {
                    svcSDKService.unicastUid(cpUid.getAnchorUid(), msg);
                }
            }

            if (isNotTheSameMember(cpUid)) {
                UserInfoVo anchorInfo = userInfos.get(cpUid.getAnchorUid());
                if (anchorInfo != null) {
                    noticeExt.put("cpNick", anchorInfo.getNick());
                }
                boolean userSkipNotice = attr.isFilterDanmaku() && commonService.inDanmuChannel(cpUid.getUserUid());
                if (!userSkipNotice) {
                    GameecologyActivity.CommonNoticeResponse.Builder tips = GameecologyActivity.CommonNoticeResponse.newBuilder()
                            .setActId(attr.getActId())
                            .setNoticeType(getComponentId() + "_some_parts_award_notice")
                            .setNoticeValue(JSON.toJSONString(noticeExt))
                            .setExtJson(StringUtils.EMPTY);

                    GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                            .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                            .setCommonNoticeResponse(tips).build();
                    if (delay > 0) {
                        delaySvcSDKServiceV2.unicastUid(cpUid.getUserUid(), msg, delay);
                    } else {
                        svcSDKService.unicastUid(cpUid.getUserUid(), msg);
                    }
                }
            }

            log.info("somePartsAwardNotice done,actId:{}, round:{}, pieceCode:{},score:{},cpUid:{}", attr.getActId(), round, pieceCode, score, JSON.toJSONString(cpUid));
        });
    }

    private void addStat(PuzzleComponentAttr attr, String seq, AwardAttrConfig awardConfig) {
        String hashKey = awardConfig.getTAwardTskId() + StringUtil.VERTICAL_BAR + awardConfig.getTAwardPkgId();
        commonDataDao.hashValueIncIgnore(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), "stat:" + seq, AWARD_STAT, hashKey, awardConfig.getNum());
    }

    public static RowCol convertToRowCol(int num) {
        if (num < 1 || num > 9) {
            throw new IllegalArgumentException("输入的数字必须在 1 到 9 之间");
        }
        int row = (num - 1) / 3 + 1;
        int col = (num - 1) % 3 + 1;
        RowCol rowCol = new RowCol();
        rowCol.setRow(row);
        rowCol.setCol(col);

        return rowCol;
    }

    private boolean isNotTheSameMember(CpUid cpUid) {
        return cpUid.getUserUid() != cpUid.getAnchorUid();
    }

    private Pair<Long, Long> getAnchorLastChannel(PuzzleComponentAttr attr, TaskProgressChanged event, long anchorUid, String dateCode) {
        // 优先取当前上报的频道
        var result = RankUtils.getHallChannel(attr.getHallActorIds(), event.getActors());
        if (result != null) {
            return result;
        }

        // 再取主播当前开播的频道
        ChannelInfoVo channel = onMicService.getOnMicChannel(anchorUid);
        if (channel != null) {
            return Pair.of(channel.getSid(), channel.getSsid());
        }

        // 最后根据记录的最新cp
        Cmpt2061LatestCp latestCp = latestCpComponent.getLatestCp(attr.getActId(), attr.getLatestCpIndex(), dateCode, attr.getRankId(), anchorUid);
        if (latestCp != null) {
            return Pair.of(latestCp.getSid(), latestCp.getSsid());
        }

        return null;
    }

    public static int getFtsBcTypeByBroType(int broType) {
        return switch (broType) {
            case BroadcastType.SUB_CHANNEL -> FstAppBroadcastType.SUB_CHANNEL;
            case BroadcastType.TOP_CHANNEL, BroadcastType.FAMILY -> FstAppBroadcastType.TOP_CHANNEL;
            case BroadcastType.ALL_ACT -> FstAppBroadcastType.ALL_ACT;
            default -> FstAppBroadcastType.ALL_TEMPLATE;
        };
    }

    private void sendStatInfoflowNotice(PuzzleComponentAttr attr) {
        Map<String, String> entries = commonDataDao.hashGetAll(attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(), AWARD_STAT);
        List<AwardStat> awardList = Collections.emptyList();
        if (MapUtils.isNotEmpty(entries)) {
            awardList = new ArrayList<>(entries.size());
            for (var entry : entries.entrySet()) {
                String key = entry.getKey();
                var tskPkgId = RankUtils.getCpUidByMemberId(key);
                if (tskPkgId == null) {
                    continue;
                }

                var awardModel = hdztAwardServiceClient.queryAwardTask(tskPkgId.getLeft(), tskPkgId.getRight());
                if (awardModel == null) {
                    continue;
                }

                String awardDetail = HdztAwardServiceClient.getAwardDetail(awardModel, 1);
                long awardCount = Convert.toLong(entry.getValue());

                awardList.add(new AwardStat(awardDetail, awardCount));
            }
        }

        long totalAmount = getAwardPoolLeft(attr);

        Map<String, Object> data = Map.of("awardList", awardList, "totalAmount", totalAmount);
        String message;
        try {
            message = FreeMarkerTemplateUtils.processTemplateIntoString(TEMPLATE, data);
        } catch (Exception e) {
            log.error("processTemplate exception:", e);
            return;
        }

        String msg = buildActRuliuMsg(attr.getActId(), false, "拼图玩法累计发放统计", message);
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_ACT_TURNOVER, msg, Collections.emptyList());
    }

    @Data
    private static class UpdatePoolStatus {
        /**
         * 是否超过限额
         */
        private Boolean isOver;

        /**
         * 是否更新过奖池
         */
        private Boolean updated;

        /**
         * 更新金额
         */
        private Long beforeAmount;

        /**
         * 更新金额
         */
        private Long afterAmount;

        public UpdatePoolStatus(boolean over, boolean updated) {
            this.isOver = over;
            this.updated = updated;
        }
    }


    @Getter
    @Setter
    public static class CpMission {
        private int round;

        private int level;

        private long score;

        private String nick;

        private String avatar;

//        private Map<String, Award> awards;
        private Map<Integer, Map<String, Award>> allAwards;

        private List<Mission> missions;

        private List<RowCol> rowColLight;

        private Map<Integer, List<RowCol>> allRowColsLight;
    }

    @Data
    public static class Award {
        private String pic;
        private String name;
    }

    @Data
    private static class Mission {
        private long taskId;

        private boolean finish;

        private long score;
    }

    @Data
    public static class RowCol {
        private int row;
        private int col;

        public RowCol() {}

        public RowCol(int row, int col) {
            this.row = row;
            this.col = col;
        }
    }

    @Data
    public static class LimitInfo {
        private long total;
        private long left;
    }

    @Getter
    @Setter
    public static class AwardStat {
        protected String awardDetail;

        protected long awardCount;

        public AwardStat() {
        }

        public AwardStat(String awardDetail, long awardCount) {
            this.awardDetail = awardDetail;
            this.awardCount = awardCount;
        }
    }
}

