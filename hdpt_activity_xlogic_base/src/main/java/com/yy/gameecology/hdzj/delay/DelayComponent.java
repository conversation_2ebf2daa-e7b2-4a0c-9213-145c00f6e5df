package com.yy.gameecology.hdzj.delay;

import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import lombok.Getter;
import lombok.Setter;
import org.springframework.scheduling.config.ScheduledTask;

import java.util.HashMap;
import java.util.Map;
import java.util.function.BiConsumer;

@Getter
@Setter
public class DelayComponent<T extends ComponentAttr> {

    /**
     * 组件
     */
    protected BaseActComponent<T> component;

    /**
     * 延迟消息处理器
     */
    protected BiConsumer<T, Object> consumer;

    /**
     * 用来记录已经启动的动态定时消费任务<br/>
     * key1 -> actId<br/>
     * key2 -> componentIndex<br/>
     * value -> dynamic scheduled task
     */
    protected final Map<Long, Map<Long, ScheduledTask>> scheduledTasks = new HashMap<>();

    @Override
    public String toString() {
        return "component:" + component.getComponentId() + ", task size=" + (scheduledTasks.size());
    }
}
