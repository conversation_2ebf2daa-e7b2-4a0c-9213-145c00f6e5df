package com.yy.gameecology.hdzj.element.component.attr;

import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CPWatchwordLotteryQueryComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "口令抽奖组件索引", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class), remark = "多个使用英文逗号隔开")
    protected List<Long> watchwordLotteryIndexes;

    @ComponentAttrField(labelText = "数量限制", remark = "小于等于0 不限制")
    protected int countLimit = 10;

    @ComponentAttrField(labelText = "额外抽奖组件索引", subFields = @SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class), remark = "多个使用英文逗号隔开")
    protected List<Long> additionIndexes;

    @ComponentAttrField(labelText = "额外索引限制")
    protected int additionCount;
}
