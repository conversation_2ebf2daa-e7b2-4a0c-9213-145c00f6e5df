package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.rankroleinfo.RoleItem;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.rolebuilder.RoleBuilder;
import com.yy.gameecology.activity.service.ActSupportService;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.PkOverTakeComponentAttr;
import com.yy.thrift.hdztranking.GroupMemberItem;
import com.yy.thrift.hdztranking.PkGroupItem;
import com.yy.thrift.hdztranking.PkInfo;
import com.yy.thrift.hdztranking.RankingInfo;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * PK反超组件
 * <p>
 * 由于事件存在延迟性,当中控收到分数改变事件时,会从中台查询具体的分数值，以进行比较
 * 这中间存在事件差,可能会导致发出事件时满足条件,查询分数时,由于对手又上了分,导致又不满足条件了
 *
 * <AUTHOR>
 * @date 2022/6/27 17:49
 **/
@Deprecated
@Component
public class PkOverTakeComponent extends BaseActComponent<PkOverTakeComponentAttr> {
    private static final String HASH_CACHE_KEY = "PkOverTake";

    @Override
    public Long getComponentId() {
        return ComponentId.PK_OVERTAKE;
    }

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = false)
    public void onRankingScoreChanged(RankingScoreChanged event, PkOverTakeComponentAttr attr) {
        long rankId = event.getRankId();
        long phaseId = event.getPhaseId();
        if (rankId != attr.getRankId() || phaseId != attr.getPhaseId()) {
            return;
        }
        log.info("event={},attr={}", JSON.toJSONString(event), JSON.toJSONString(attr));

        long actId = attr.getActId();
        Date now = commonService.getNow(actId);

        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfigByCache(actId, rankId, 0);
        String timeStr = "";
        long timeKey = rankingInfo.getTimeKey();
        if (timeKey == TimeKeyHelper.TIME_KEY_BY_DAY) {
            // 日榜
            timeStr = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        } else if (timeKey == TimeKeyHelper.TIME_KEY_BY_HOUR) {
            // 小时榜
            timeStr = DateUtil.format(now, DateUtil.PATTERN_YYYYMMDD);
        }

        // 查询pk数据
        String memberId = event.getMember();
        PkInfo pkInfo = hdztRankingThriftClient.queryPhasePkgroup(actId, rankId, phaseId, timeStr, timeStr, true, true, Maps.newHashMap());
        List<GroupMemberItem> groupMemberItems = findGroupMemberItem(memberId, pkInfo);
        if (groupMemberItems == null) {
            log.warn("not found groupMemberItems:{}", memberId);
            return;
        }

        PkOverTakeInfo pkOverTakeInfo = convertToPkOverTakeInfo(groupMemberItems);

        String groupCode = redisConfigManager.getGroupCode(actId);
        String hashKey = makeKey(attr, buildHashKey(timeStr));
        String luaResult = actRedisDao.pkOverTake(groupCode, hashKey, pkOverTakeInfo.getField()
                , pkOverTakeInfo.getWinnerMemberId(), Math.abs(pkOverTakeInfo.getScoreGap()), attr.getScoreThreshold());
        log.info("luaResult:{}", luaResult);
        JSONObject luaResultJson = JSONObject.parseObject(luaResult);

        boolean isOverTake = luaResultJson.getIntValue("overTake") == 1;
        if (isOverTake && inTime(now, attr.getStartTime(), attr.getEndTime())) {
            // 发生了反转,发送广播
            long roleType = rankingInfo.getRoleItemConfig().getRoles().get(0).get(0).getRoleType();
            broadcastBanner(roleType, attr, pkOverTakeInfo);
        }
    }

    private void broadcastBanner(long roleType, PkOverTakeComponentAttr attr, PkOverTakeInfo pkOverTakeInfo) {
        ActSupportService actSupportClientService = ActSupportService.getInstance(attr.getBusiId());
        RoleBuilder rankBuilder = actSupportClientService.getRankBuilder((int) roleType);
        Map<String, RoleItem> roleItemMap = rankBuilder.buildRankByYy(Sets.newHashSet(pkOverTakeInfo.getWinnerMemberId(), pkOverTakeInfo.getLoserMemberId()));

        // TODO

    }

    private String buildHashKey(String timeStr) {
        if (StringUtils.isEmpty(timeStr)) {
            return HASH_CACHE_KEY;
        }

        return HASH_CACHE_KEY + ":" + timeStr;
    }

    private boolean inTime(Date now, String startTimeStr, String endTimeStr) {
        Date startTime = DateUtil.getDate(DateUtil.format(now, "yyyy-MM-dd " + startTimeStr));
        Date endTime = DateUtil.getDate(DateUtil.format(now, "yyyy-MM-dd " + endTimeStr));

        boolean inTime = (endTime.before(startTime) && (now.before(endTime) || now.after(startTime)))
                || (endTime.after(startTime) && now.after(startTime) && now.before(endTime));

        return inTime;
    }

    private List<GroupMemberItem> findGroupMemberItem(String memberId, PkInfo pkInfo) {
        if (pkInfo == null || CollectionUtils.isEmpty(pkInfo.getPkGroupItems())) {
            return null;
        }

        for (PkGroupItem pkGroupItem : pkInfo.getPkGroupItems()) {
            if (pkGroupItem == null || CollectionUtils.isEmpty(pkGroupItem.getMemberPkItems())) {
                continue;
            }

            for (List<GroupMemberItem> memberPkItem : pkGroupItem.getMemberPkItems()) {
                if (memberPkItem.size() != 2) {
                    log.warn("memberPkItem size is not 2 :{}", JSON.toJSONString(memberPkItem));
                    continue;
                }

                if (Objects.equals(memberPkItem.get(0).getMemberId(), memberId)
                        || Objects.equals(memberPkItem.get(1).getMemberId(), memberId)) {
                    return memberPkItem;
                }
            }
        }

        return null;
    }

    private PkOverTakeInfo convertToPkOverTakeInfo(List<GroupMemberItem> groupMemberItems) {
        GroupMemberItem left = groupMemberItems.get(0);
        GroupMemberItem right = groupMemberItems.get(1);

        if (Convert.toLong(left.getMemberId()) > Convert.toLong(right.getMemberId())) {
            left = groupMemberItems.get(1);
            right = groupMemberItems.get(0);
        }

        PkOverTakeInfo info = new PkOverTakeInfo();
        info.setLeftMemberId(left.getMemberId());
        info.setLeftScore(left.getScore());
        info.setRightMemberId(right.getMemberId());
        info.setRightScore(right.getScore());
        info.setScoreGap(left.getScore() - right.getScore());

        return info;
    }

    @Data
    private static class PkOverTakeInfo {
        // 左边是id较小的
        private String leftMemberId;
        private String rightMemberId;

        private long leftScore;
        private long rightScore;

        // leftScore - rightScore
        private long scoreGap;

        public String getWinnerMemberId() {
            if (scoreGap > 0) {
                return leftMemberId;
            }

            return rightMemberId;
        }

        public String getField() {
            return leftMemberId + "_" + rightMemberId;
        }

        public String getLoserMemberId() {
            if (scoreGap > 0) {
                return rightMemberId;
            }

            return leftMemberId;
        }
    }
}
