package com.yy.gameecology.hdzj.element.component;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.bean.ChannelInfoVo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.event.AppBannerEvent2;
import com.yy.gameecology.activity.bean.event.AppBannerMp4Config;
import com.yy.gameecology.activity.bean.hdzt.PhaseTimeEnd;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.retry.RetryTool;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.common.bean.CpUid;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.BroadcastType;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.FstAppBroadcastType;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.db.model.gameecology.ComponentDataLimit;
import com.yy.gameecology.common.db.model.gameecology.ComponentDataList;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2061LatestCp;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.HeadlineTimeComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.utils.BusinessUtils;
import com.yy.thrift.broadcast.Template;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztranking.BusiId;
import com.yy.thrift.hdztranking.Rank;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.*;

@Component
@RestController
@RequestMapping("/5125")
public class HeadlineTimeComponent extends BaseActComponent<HeadlineTimeComponentAttr> {

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Override
    public Long getComponentId() {
        return ComponentId.HEADLINE_TIME;
    }

    // cp奖励key cp_reward_info:{yyyymmddhh}
    public static final String CP_REWARD_INFO = "cp_reward_info:%s";

    // cp奖励key hdcp_reward_record:{yyyymmddhh}
    public static final String CP_REWARD_SEQ_PREFIX = "hdcp_reward_record:%s";

    // cp奖励总金额
    public static final String CP_REWARD_LIMIT = "headline_cp_reward_limit";

    private static final String ROBOT_TOKEN_PREFIX = "https://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=";

    // 组件banner_id 最强cp广播 组件广播
    private static final long HEADLINE_TIME_BRO_BANNER_ID = 5125001;

    private static final int HEADLINE_TIME_CHAT_LOTTERY_EXPIRE = 60; // 过期时间 60m

    private static final String INFOFLOW_MSG_TEMPLATE = """
            场次：${dateCode}
            本轮头条CP：
            | 角色 | 昵称 | UID | 头像地址 |
            | --- | ---- | --- | ------ |
            | 主播 | ${anchorNick} | ${anchorUid?c} | ${anchorAvatar} |
            | 神豪 | ${userNick} | ${userUid?c} | ${userAvatar} |
            
            其他信息:
            |     |     |
            | --- | --- |
            | CP值 | ${score} |
            | 飞机票 | yy://pd-[sid=${sid?c}&ssid=${ssid?c}] |
            | 发放数量 | ${giftCount?c} |
            | 发放信息 | ${awardDetail} |
            | 累计发放金额（单位：厘） | ${totalAmount} |
            """;

    private static final freemarker.template.Template TEMPLATE;

    static {
        try {
            TEMPLATE = new freemarker.template.Template("infoflow_5125", INFOFLOW_MSG_TEMPLATE, null);
        } catch (IOException e) {
            throw new IllegalStateException(e);
        }
    }

    @Autowired
    protected WebdbThriftClient webdbThriftClient;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private ChannelWatchwordLotteryComponent channelWatchwordLotteryComponent;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private CommonDataDao commonDataDao;

    @Autowired
    private LatestCpComponent latestCpComponent;

    @Autowired
    private OnlineNoticeComponent onlineNoticeComponent;

    private String chatLotteryBoxSeq(HeadlineTimeComponentAttr attr,String hourCode){
        return makeKey(attr, "seq:sendChatLottery:" + hourCode);
    }

    private boolean checkInDanmuChannel(HeadlineTimeComponentAttr attr,long sid,long ssid){
        boolean check = false;
        if (attr.getExcludeDanmuChannel() > 0 ){
            check =  commonService.isDanmuChannel(sid,ssid);
        }
        log.info("checkInDanmuChannel check:{} {}", check,attr.getExcludeDanmuChannel());
        return check;
    }

    @RequestMapping("/testNotice")
    public Response<?> testNotice(long actId, long comIndex) {
        HeadlineTimeComponentAttr attr = getComponentAttr(actId, comIndex);
        if (attr == null) {
            return Response.fail(-1, "活动未配置");
        }
        long uid = getLoginYYUid();
        AwardAttrConfig awardAttrConfig = attr.getCpTaskPackageReward().get(100000L);
        JSONObject noticeExt = new JSONObject();
        var awardModel = hdztAwardServiceClient.queryAwardTask(awardAttrConfig.getTAwardTskId(), awardAttrConfig.getTAwardPkgId());
        if (awardModel != null) {
            String giftDetail = HdztAwardServiceClient.getAwardDetail(awardModel, awardAttrConfig.getNum());
            noticeExt.put("giftDetail", giftDetail);
            noticeExt.put("giftIcon", awardModel.getPackageImage());
            log.info("testNotice with:{} uid:{}", noticeExt,uid);
            commonBroadCastService.commonNoticeUnicast(attr.getActId(), getComponentId() + "_top_award_pop", JSON.toJSONString(noticeExt), StringUtils.EMPTY, uid);
        }
        return Response.ok();
    }

    @RequestMapping("/awardLeft")
    public Response<awardLeft> awardLeft(long actId, long comIndex) {
        HeadlineTimeComponentAttr attr = getComponentAttr(actId, comIndex);
        if (attr == null) {
            return Response.fail(-1, "活动未配置");
        }
        awardLeft l = new awardLeft();
        l.setTotal(attr.getCpAwardLimit()/1000);
        l.setLeft((int)getAwardPoolLeft(attr)/1000);
        return Response.success(l);
    }

    private long getAwardPoolLeft(HeadlineTimeComponentAttr attr) {
        ComponentDataLimit where = new ComponentDataLimit();
        where.setActId(attr.getActId());
        where.setComponentId(attr.getCmptId());
        where.setCmptUseInx(attr.getCmptUseInx());
        where.setKeyName(CP_REWARD_LIMIT);
        ComponentDataLimit dataLimit = gameecologyDao.selectOne(ComponentDataLimit.class, where, "", ComponentDataLimit.getTableName(attr.getActId()), false);
        long send = dataLimit == null ? 0L : Convert.toLong(dataLimit.getValue());
        return Math.max(Convert.toLong(attr.getCpAwardLimit()) - send, 0);
    }

    /**
     * 过去过期cp
     */
    @RequestMapping("/getPastBestCp")
    public Response<BestCpData> getPastBestCp(long actId, long comIndex, String dateStr) {
        Date now = commonService.getNow(actId);
        String key = String.format(CP_REWARD_INFO, dateStr);
        String timeCode = DateUtil.format(DateUtil.addMinutes(now,-60), DateUtil.PATTERN_TYPE7);
        HeadlineTimeComponentAttr attr = getComponentAttr(actId, comIndex);
        if (attr == null) {
            return Response.fail(-1, "活动未配置");
        }

        BestCpData bestCpData = new BestCpData();
        bestCpData.setPrevHour(false);

        ComponentDataList info = commonDataDao.listSelectOne(attr.getActId(), attr.getCmptId(), (int) attr.getCmptUseInx(), key);
        if (info == null || StringUtils.isEmpty(info.getValue())) {
            bestCpData.setShow(false);
            return Response.success(bestCpData);
        }

        bestCpData = JSONObject.parseObject(info.getValue(), BestCpData.class);
        bestCpData.setShow(true);

        long bossUid = bestCpData.getUid();
        long anchorUid = bestCpData.getAnchorUid();
//        bestCpData.setCpVersion(bossUid == uid || anchorUid == uid);
//        List<Pop> popList = new ArrayList<>();
        LastBestCp lastBestCp = getUserNickExt(bossUid, anchorUid);
        bestCpData.setUserNick(lastBestCp.getWinnerBossNick());
        bestCpData.setUserAvatar(lastBestCp.getWinnerBossLogo());
        bestCpData.setAnchorNick(lastBestCp.getWinnerAnchorNick());
        bestCpData.setAnchorAvatar(lastBestCp.getWinnerAnchorLogo());
        bestCpData.setNickExtUsers(lastBestCp.getNickExtUsers());
        String seq = chatLotteryBoxSeq(attr, dateStr);
        ChannelWatchwordLotteryComponent.ChatLotteryBoxInfo lotteryInfo = channelWatchwordLotteryComponent.getLotteryCountAndExpiredTime(attr.getActId(),attr.getChatLotteryIndex(), seq);
        if (lotteryInfo == null) {
            return Response.fail(-1, "活动未配置");
        }
        bestCpData.setChatLotteryNum(lotteryInfo.getLotteryNum());
        if (timeCode.equals(dateStr)) {
            bestCpData.setPrevHour(true);
            String chatText = lotteryInfo.getChatLotteryText();
            chatText = StringUtils.replace(chatText, "{userNick}", lastBestCp.getWinnerBossNick());
            chatText = StringUtils.replace(chatText, "{babyNick}", lastBestCp.getWinnerAnchorNick());
            bestCpData.setChatText(chatText);
            bestCpData.setSid(lotteryInfo.getSid());
            bestCpData.setSsid(lotteryInfo.getSsid());
        }

        return Response.success(bestCpData);
    }

    // 结算 计算发奖 记录发觉将信息 增加口令抽奖 下发广播  相关信息通知到如流
    @HdzjEventHandler(value = PhaseTimeEnd.class, canRetry = true)
    public void onRankingTimeEnd(PhaseTimeEnd event, HeadlineTimeComponentAttr attr) {
        log.info("onRankingTimeEnd event:{}", JsonUtil.toJson(event));
        if (event.getPhaseId() != attr.getPhaseId() || event.getRankId() != attr.getRankId()) {
            return;
        }

        Date lastHour = DateUtil.getDate(event.getEndTime());
        String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), lastHour);
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(attr.getActId(), attr.getRankId(), attr.getPhaseId(), timeCode, 1, Collections.emptyMap());
        if (CollectionUtils.isEmpty(ranks)) {
            log.warn("onRankingTimeEnd last rank is empty");
            return;
        }
        Rank rank = ranks.getFirst();
        log.info("onRankingTimeEnd rank one {}",rank);

        CpUid cpUid = Const.splitCpMember(rank.getMember());
        final long bossUid = cpUid.getUserUid(), anchorUid = cpUid.getAnchorUid(), score = rank.getScore();

        if (bossUid == 0 || anchorUid == 0 || score == 0) {
            log.info("onRankingTimeEnd last rank is empty");
            return;
        }

        LastBestCp lastBestCp = getUserNickExt(bossUid, anchorUid);

        final String seq = makeKey(attr, StringUtil.isNotBlank(event.getEkey()) ? event.getEkey() : event.getSeq());

        // 发奖 限额 记录时段奖励
        AwardInfo awardInfo = settleHourRankAward(attr, bossUid, anchorUid, score, timeCode, seq);
        log.info("onRankingTimeEnd awardInfo is {}", awardInfo);
        AwardAttrConfig awardAttrConfig = awardInfo.getAwardConfig();

        ChannelInfoVo channelInfoVo = new ChannelInfoVo();
        ChannelInfoVo anchorUidOnlineInfo = getUserOnlineInfo(anchorUid);
        if (anchorUidOnlineInfo.getSid() > 0 && anchorUidOnlineInfo.getSsid() > 0 && !checkInDanmuChannel(attr,anchorUidOnlineInfo.getSid(),anchorUidOnlineInfo.getSsid() ) ) {
            log.info("onRankingTimeEnd use online sid {}", JsonUtil.toJson(anchorUidOnlineInfo));
            channelInfoVo.setSid(anchorUidOnlineInfo.getSid());
            channelInfoVo.setSsid(anchorUidOnlineInfo.getSsid());
        } else {
            Cmpt2061LatestCp sidSsidInfo = latestCpComponent.getLatestCp(attr.getActId(), attr.getLastCpIndex(), timeCode, attr.getRankId(), anchorUid);
            log.info("onRankingTimeEnd sidSsidInfo {} ",JsonUtil.toJson(sidSsidInfo));
            long sid = sidSsidInfo != null ? sidSsidInfo.getSid() : 0;
            long ssid = sidSsidInfo != null ? sidSsidInfo.getSsid() : 0;
            if (ssid == 0 ||sid == 0) {
                log.error("last channel empty");
            }
            channelInfoVo.setSid(sid);
            channelInfoVo.setSsid(ssid);
        }

        // 增加口令抽奖
        addChatLotteryBox(attr, rank.getMember(), channelInfoVo, timeCode);

        // 记录发奖情况
        recordCpAwardInfo(attr, lastBestCp, channelInfoVo, score, awardAttrConfig, timeCode);

        // 相关广播 cp奖励弹窗
        settleHourRankAwardBro(attr, lastBestCp, score, awardAttrConfig, channelInfoVo, timeCode);

        threadPoolManager.get(Const.GENERAL_POOL).execute(() -> sendNotice(attr, seq, awardAttrConfig, lastBestCp, score, awardInfo.getUpdatePoolStatus().getAfterAmount(), channelInfoVo, timeCode));
    }

    private LastBestCp getUserNickExt(long bossUid, long anchorUid) {
        String nick = StringUtils.EMPTY;
        String logo = StringUtils.EMPTY;
        String anchorNick = StringUtils.EMPTY;
        String anchorLogo = StringUtils.EMPTY;
        LastBestCp lastBestCp = new LastBestCp();
        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(2);
        Map<Long, UserInfoVo> userInfoMap = userInfoService.getCpUserInfoWithNickExt1(bossUid, anchorUid, multiNickUsers);
        var bossUserInfo = userInfoMap.get(bossUid);
        if (bossUserInfo != null) {
            nick = bossUserInfo.getNick();
            logo = bossUserInfo.getAvatarUrl();
        }
        var anchorUserInfo = userInfoMap.get(anchorUid);
        if (anchorUserInfo != null) {
            anchorNick = anchorUserInfo.getNick();
            anchorLogo = anchorUserInfo.getAvatarUrl();
        }
        lastBestCp.setUserId(bossUid);
        lastBestCp.setAnchorUid(anchorUid);
        lastBestCp.setWinnerAnchorNick(anchorNick);
        lastBestCp.setWinnerAnchorLogo(anchorLogo);
        lastBestCp.setWinnerBossNick(nick);
        lastBestCp.setWinnerBossLogo(logo);
        lastBestCp.setNickExtUsers(multiNickUsers);
        return lastBestCp;
    }

    // 每小时cp 发奖
    private AwardInfo settleHourRankAward(HeadlineTimeComponentAttr attr, long bossUid, long anchorId, long score, String preHourTimeCode, String seq) {
        AwardInfo ret = new AwardInfo();

        String time = DateUtil.format(commonService.getNow(attr.getActId()));

        String awardSourceSeq = String.format("hd_limit_%s_%s", preHourTimeCode, seq);

        CommonDataDao.LimitUpdateState limitInfo = commonDataDao.valueGetSnapshot(attr.getActId(), attr.getCmptId(), (int) attr.getCmptUseInx(), CP_REWARD_LIMIT, awardSourceSeq);

        long targetScoreConfig = 0;
        List<Long> scoreConfigs = attr.getCpTaskPackageReward().keySet().stream().sorted().toList();
        for (long scoreConfig : scoreConfigs) {
            if (score >= scoreConfig) {
                targetScoreConfig = scoreConfig;
            }
        }
        AwardAttrConfig awardConfig = attr.getCpTaskPackageReward().get(targetScoreConfig);

        boolean isSameUser = anchorId == bossUid;
        UpdatePoolStatus statusRet = getPoolStatus(attr, limitInfo, awardConfig, isSameUser);

        // 超过限额且 达到发奖条件 则发入场秀等
        if (awardConfig.getTAwardPkgId() > 0 && statusRet.getIsOver()) {
            log.info("settleHourRankAward award pool out release,uid:{},anchorUid:{},awardConfig:{}", bossUid, anchorId, JSON.toJSONString(awardConfig));
            awardConfig = attr.getCpTaskPackageReward().get(-1L);
        }

        if (!statusRet.getUpdated()) {
            long limit = awardConfig.getAwardAmount();
            if (anchorId != bossUid) {
                limit = awardConfig.getAwardAmount() * 2;
            }
            commonDataDao.valueIncrIgnore(attr.getActId(), attr.getCmptId(), (int) attr.getCmptUseInx(), awardSourceSeq, CP_REWARD_LIMIT, limit); // 要发两个人的
        }

        log.info("settleHourRankAward award pool info bossUid:{},anchorId:{},seq:{},limitInfo:{},statusRet:{},awardConfig:{}", bossUid, anchorId,
                seq, limitInfo, statusRet, JSON.toJSONString(awardConfig));

        log.info("settleHourRankAward award pool info taskId:{},packageId:{}", awardConfig.getTAwardTskId(), awardConfig.getTAwardPkgId());

        Map<Long, Integer> packageIdAmount = ImmutableMap.of(awardConfig.getTAwardPkgId(), awardConfig.getNum());
        if (awardConfig.getTAwardPkgId() > 0) {
            if (anchorId != bossUid) {
                String awardUserSeq = makeKey(attr, awardSourceSeq + ":user:" + bossUid);
                String awardUserSeqMd5 = MD5SHAUtil.getMD5(awardUserSeq);
                hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.GAME_ECOLOGY.getValue(), bossUid, awardConfig.getTAwardTskId(), ImmutableMap.of(awardConfig.getTAwardTskId(), packageIdAmount), awardUserSeqMd5, Collections.emptyMap());
            }
            String awardAnchorSeq = makeKey(attr, awardSourceSeq + ":anchor:" + anchorId);
            String awardAnchorSeqMd5 = MD5SHAUtil.getMD5(awardAnchorSeq);
            hdztAwardServiceClient.doBatchWelfareV2(time, BusiId.GAME_ECOLOGY.getValue(), anchorId, awardConfig.getTAwardTskId(), ImmutableMap.of(awardConfig.getTAwardTskId(), packageIdAmount), awardAnchorSeqMd5, Collections.emptyMap());
        }
        ret.setAwardConfig(awardConfig);
        ret.setUpdatePoolStatus(statusRet);

        return ret;
    }

    private UpdatePoolStatus getPoolStatus(HeadlineTimeComponentAttr attr, CommonDataDao.LimitUpdateState limitInfo, AwardAttrConfig awardConfig,boolean isSameUser) {
        UpdatePoolStatus ret = new UpdatePoolStatus(false, false);
        if (limitInfo.getUpdateSate() > 0) { // 更新过
            ret.setUpdated(true);
            ret.setAfterAmount(limitInfo.getAfterValue());
            ret.setBeforeAmount(limitInfo.getBeforeValue());
            if (ret.getBeforeAmount() >= attr.getCpAwardLimit()) {
                ret.setIsOver(true);
            }
        } else { //  没有更新过
            ret.setBeforeAmount(limitInfo.getBeforeValue());
            if (ret.getBeforeAmount() < attr.getCpAwardLimit()) {
                if (isSameUser) {
                    ret.setAfterAmount(ret.getBeforeAmount() + awardConfig.getAwardAmount());
                }else {
                    ret.setAfterAmount(ret.getBeforeAmount() + awardConfig.getAwardAmount() * 2);
                }
            } else {
                ret.setIsOver(true);
                ret.setAfterAmount(ret.getBeforeAmount());
            }
        }
        return ret;
    }

    private void addChatLotteryBox(HeadlineTimeComponentAttr attr, String memberId, ChannelInfoVo anchorChannelInfoVo, String preHourTimeCode) {
        String seq = chatLotteryBoxSeq(attr, preHourTimeCode);
        Date now = commonService.getNow(attr.getActId());
        log.info("addChatLotteryBox seq:{} {} {} {}", seq,memberId,anchorChannelInfoVo,preHourTimeCode);
        //用户跳转的频道
        channelWatchwordLotteryComponent.addWatchwordLotteryBox(attr.getActId(), attr.getChatLotteryIndex(), seq,
                memberId, anchorChannelInfoVo.getSid(), anchorChannelInfoVo.getSsid(), DateUtil.addMinutes(now, HEADLINE_TIME_CHAT_LOTTERY_EXPIRE));
    }

    private void sendNotice(HeadlineTimeComponentAttr attr, String seq, AwardAttrConfig awardConfig, LastBestCp bestCp, long score, long awardAmount, ChannelInfoVo anchorChannelInfo, String hourCode) {
        var cpInfo = buildRuliuMsg(attr, awardConfig, bestCp, score, anchorChannelInfo, awardAmount, hourCode);
        seq = DigestUtil.sha256Hex(seq);
        var rs = commonDataDao.listInsertIgnore(attr.getActId(), attr.getCmptId(), (int) attr.getCmptUseInx(), seq, "best_cp_list", JSON.toJSONString(cpInfo));
        log.info("sendNotice insert best cp list with dateCode:{} rs:{}", hourCode, rs);

        String message;
        try {
            message = FreeMarkerTemplateUtils.processTemplateIntoString(TEMPLATE, cpInfo);
        } catch (Exception e) {
            log.error("processTemplate exception:", e);
            throw new RuntimeException(e);
        }

        String msg = commonService.buildActRuliuMsg(attr.getActId(), false, "小时cp玩法", message);
        baiduInfoFlowRobotService.sendNotifyByConfigKey(GeParamName.IMGroup.IMG_ACT_TURNOVER, msg, null);
        log.info("settleHourRankAward sendNotice done@msg:{},groupId:{}", msg, attr.getGroupId());
    }

    private InfoflowCpInfo buildRuliuMsg(HeadlineTimeComponentAttr attr, AwardAttrConfig awardConfig, LastBestCp bestCp, long score, ChannelInfoVo anchorChannelInfo, long awardAmount, String hourCode) {
        InfoflowCpInfo data = new InfoflowCpInfo();
        data.setDateCode(hourCode);
        data.setUserUid(bestCp.getUserId());
        data.setUserNick(StringUtils.replaceAll(bestCp.getWinnerBossNick(), "\\|", "\\\\|"));
        data.setUserAvatar(bestCp.getWinnerBossLogo());
        data.setAnchorUid(bestCp.getAnchorUid());
        data.setAnchorNick(StringUtils.replaceAll(bestCp.getWinnerAnchorNick(), "\\|", "\\\\|"));
        data.setAnchorAvatar(bestCp.getWinnerAnchorLogo());
        data.setSid(anchorChannelInfo.getSid());
        data.setSsid(anchorChannelInfo.getSsid());
        data.setScore(score);

        var awardModel = hdztAwardServiceClient.queryAwardTask(awardConfig.getTAwardTskId(), awardConfig.getTAwardPkgId());
        int giftCount = awardConfig.getNum();
        String awardDetail = awardConfig.getAwardName();
        if (awardModel != null) {
            awardDetail = HdztAwardServiceClient.getAwardDetail(awardModel, awardConfig.getNum());
            giftCount = HdztAwardServiceClient.getAwardCount(awardModel, awardConfig.getNum());
        }

        data.setAwardDetail(awardDetail);
        data.setGiftCount(giftCount);
        data.setTotalAmount(awardAmount);

        return data;
    }


    // mp4 特效
    public void settleHourRankAwardBro(HeadlineTimeComponentAttr attr, LastBestCp lastBestCp, long score, AwardAttrConfig awardConfig, ChannelInfoVo channelInfoVo, String preHourTimeCode) {
        final long bossUid = lastBestCp.getUserId(), anchorUid = lastBestCp.getAnchorUid();
        log.info("settleHourRankAwardBro bossUid:{} anchorUid:{} score:{} {} {}", bossUid, anchorUid, score, channelInfoVo, preHourTimeCode);
        Map<String, Object> ext = Maps.newHashMap();
        ext.put("sid", channelInfoVo.getSid());
        ext.put("ssid", channelInfoVo.getSsid());
        ext.put("userUid", bossUid);
        ext.put("babyUid", anchorUid);
        ext.put("userLogo", lastBestCp.getWinnerBossLogo());
        ext.put("userNick", Base64.encode(lastBestCp.getWinnerBossNick()));
        ext.put("babyLogo", lastBestCp.getWinnerAnchorLogo());
        ext.put("babyNick", Base64.encode(lastBestCp.getWinnerAnchorNick()));
        ext.put("nickExtUsers", JsonUtil.toJson(lastBestCp.getNickExtUsers()));

        AwardModelInfo awardPackage = hdztAwardServiceClient.queryAwardTask(awardConfig.getTAwardTskId(), awardConfig.getTAwardPkgId());

        if (awardPackage != null) {
            ext.put("giftName", awardPackage.getPackageName());
            ext.put("giftIcon", awardPackage.getPackageImage());
            ext.put("giftUnit", awardPackage.getUnit());

            int count = HdztAwardServiceClient.getAwardCount(awardPackage, awardConfig.getNum());
            ext.put("giftCount", count);
        }

        log.info("settleHourRankAwardBro {}",JsonUtil.toJson(awardConfig));
        String broSeq = makeKey(attr, "seq:settleHourRankAwardBro:" + preHourTimeCode);
        RetryTool.withRetryCheck(attr.getActId(), broSeq, () -> {
            Template template = BusinessUtils.getTemplateByBusiId(attr.getBusiId());
            if (attr.getExcludeDanmuChannel() > 0 ){
                log.info("ExcludeDanmuChannel bro{} ",attr.getExcludeDanmuChannel());
                commonBroadCastService.commonBannerBroadcastAllTemplateExcludeDanmu(template, attr.getActId(), 0L, 0L, HEADLINE_TIME_BRO_BANNER_ID, 0L, ext);
            }else {
                commonBroadCastService.commonBannerBroadcast(0, 0, 0, template, BroadcastType.ALL_TEMPLATE, attr.getActId(), 0L, 0L, HEADLINE_TIME_BRO_BANNER_ID, 0L, ext);
            }
        });

        //app toast
        if (awardPackage != null) {
            log.info("settleHourRankAwardBro app toast");
            JSONObject noticeExt = new JSONObject();
            int count = HdztAwardServiceClient.getAwardCount(awardPackage, awardConfig.getNum());
            noticeExt.put("giftDetail", awardPackage.getPackageName() + count + awardPackage.getUnit());
            noticeExt.put("giftIcon", awardPackage.getPackageImage());

            String noticeType = getComponentId() + "_top_award_pop";
            String noticeValue = JSON.toJSONString(noticeExt);
            onlineNoticeComponent.sendOnlineNotice(attr.getActId(), attr.getOnlineNoticeIndex(), bossUid, broSeq, noticeType, noticeValue, 7);
            if (anchorUid != bossUid) {
                onlineNoticeComponent.sendOnlineNotice(attr.getActId(), attr.getOnlineNoticeIndex(), anchorUid, broSeq, noticeType, noticeValue, 7);
            }
        }

        //---app mp4 特效
        AppBannerMp4Config mp4Config = new AppBannerMp4Config();
        List<Map<String, String>> broContentLayers = getMp4TextConfig(attr, lastBestCp);
        mp4Config.setLayerExtKeyValues(broContentLayers);
        mp4Config.setUrl(attr.getMp4Url());
        mp4Config.setLevel(attr.getMp4Level());

        String appBannerSeq = makeKey(attr, "seq:appBanner1:" + preHourTimeCode);
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), appBannerSeq,
                BroadCastHelpService.toAppBroBusiness(attr.getBusiId()), FstAppBroadcastType.ALL_TEMPLATE, 0, 0, "", Lists.newArrayList());
        appBannerEvent.setMp4Config(mp4Config);
        final int mp4ContentType = 5;
        appBannerEvent.setContentType(mp4ContentType);
        appBannerEvent.setAppId(commonService.getTurnoverAppId(Convert.toInt(attr.getBusiId())));
        appBannerEvent.setUidList(Lists.newArrayList(bossUid, anchorUid));
        log.info("appBannerEvent:{}", appBannerEvent);
        RetryTool.withRetryCheck(attr.getActId(), appBannerSeq, () -> {
            if (attr.getExcludeDanmuChannel() > 0 ){
                kafkaService.sendAppBannerKafkaExcludeDanmuku(appBannerEvent);
            }else {
                kafkaService.sendAppBannerKafka(appBannerEvent);
            }
        });
    }

    private ChannelInfoVo getUserOnlineInfo(long uid) {
        ChannelInfoVo channelInfoVo = new ChannelInfoVo();
        UserCurrentChannel bossUidChannel = commonService.getNoCacheUserCurrentChannel(uid, 1);
        log.info("getUserOnlineInfo uid:{},bossUidChannel:{}", uid, bossUidChannel);
        if (bossUidChannel != null && bossUidChannel.getSubsid() > 0 && bossUidChannel.getTopsid() > 0) {
            log.info("getUserOnlineInfo uid:{},bossUidChannel:{}", uid, bossUidChannel.getTopsid());
            channelInfoVo.setSid(bossUidChannel.getTopsid());
            channelInfoVo.setSsid(bossUidChannel.getSubsid());
            return channelInfoVo;
        }
        channelInfoVo.setSid(0);
        channelInfoVo.setSsid(0);
        return channelInfoVo;
    }


    private List<Map<String, String>> getMp4TextConfig(HeadlineTimeComponentAttr attr, LastBestCp lastBestCp) {
        List<Map<String, String>> result = Lists.newArrayList();
        for (String key : attr.getMp4LayerExtKeyValues().keySet()) {
            Map<String, String> keyMap = Maps.newHashMap();
            String text = attr.getMp4LayerExtKeyValues().get(key);
            if (StringUtils.isNotBlank(text)) {
                //替换uid
                text = text.replace("{userNick}", "{" + lastBestCp.getUserId() + ":n}");
                text = text.replace("{anchorNick}", "{" + lastBestCp.getAnchorUid() + ":n}");
                text = text.replace("{userLogo}", lastBestCp.getWinnerBossLogo());
                text = text.replace("{anchorLogo}", lastBestCp.getWinnerAnchorLogo());
            }

            keyMap.put(key, text);
            result.add(keyMap);
        }
        return result;
    }

    private void recordCpAwardInfo(HeadlineTimeComponentAttr attr, LastBestCp lastCp, ChannelInfoVo channelInfo, long score, AwardAttrConfig awardAttrConfig, String hourCode) {
        BestCpData cpData = new BestCpData();
        cpData.setAnchorUid(lastCp.getAnchorUid());
        cpData.setUid(lastCp.getUserId());
        final long packageId = awardAttrConfig.getTAwardPkgId();
        cpData.setPackageId(packageId);
        var awardPackage = hdztAwardServiceClient.queryAwardTask(awardAttrConfig.getTAwardTskId(), awardAttrConfig.getTAwardTskId());
        if (awardPackage != null) {
            String awardDetail = HdztAwardServiceClient.getAwardDetail(awardPackage, awardAttrConfig.getNum());
            cpData.setAwardName(awardDetail);
        }
        cpData.setScore(score);
        cpData.setSid(channelInfo.getSid());
        cpData.setSsid(channelInfo.getSsid());
        String key = String.format(CP_REWARD_INFO, hourCode);
        String seq = makeKey(attr, String.format(CP_REWARD_SEQ_PREFIX, hourCode));
        String value = JsonUtil.toJson(cpData);
        commonDataDao.listInsertIgnore(attr.getActId(), attr.getCmptId(), (int) attr.getCmptUseInx(), seq, key, value);
    }

    @Data
    public static class AwardInfo {
        private AwardAttrConfig awardConfig;
        private UpdatePoolStatus updatePoolStatus;
    }

    @Data
    public static class UpdatePoolStatus {
        private Boolean isOver; // 是否超过限额
        private Boolean updated; // 是否更新过奖池
        private Long beforeAmount; // 更新金额
        private Long afterAmount; // 更新金额

        public UpdatePoolStatus(boolean over, boolean updated) {
            this.isOver = over; // 初始化 name
            this.updated = updated;   // 初始化 age
        }
    }

    @Data
    public static class LastBestCp {

        private long userId;

        private long anchorUid;

        private String winnerAnchorNick;

        private String winnerAnchorLogo;

        private String winnerBossNick;

        private String winnerBossLogo;

        private Map<String, Map<String, MultiNickItem>> nickExtUsers = new HashMap<>();
    }

    @Data
    public static class BestCpData {
        private String anchorNick;

        private String anchorAvatar;

        private String userNick;

        private String userAvatar;

//        private boolean visit;

//        private long visitNums;

        private boolean show;

        private boolean cpVersion;

//        private List<Pop> popList;

        // 奖励信息
        private String awardName;

        // 奖励图标
//        private String awardIcon;

        // 奖包信息
        private long packageId;

        private long sid;

        private long ssid;

        private Map<String, Map<String, MultiNickItem>> nickExtUsers;

        private long uid;

        private long anchorUid;

        // 榜单数值
        private long score;

        // 口令抽奖text
        private String chatText;

        // 真为 最近的上一个时段 需要口令抽奖
        private boolean isPrevHour;

        private long chatLotteryNum; // 膜拜人数

    }

    @Data
    public static class awardLeft{
        private long total;
        private long left;
    }

    @Getter
    @Setter
    public static class InfoflowCpInfo {
        protected String dateCode;

        protected long userUid;

        protected long anchorUid;

        protected String userNick;

        protected String anchorNick;

        protected String userAvatar;

        protected String anchorAvatar;

        protected long score;

        protected long sid;

        protected long ssid;

        protected long giftCount;

        protected String awardDetail;

        protected long totalAmount;
    }
}
