package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.BlackListComponentAttr;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023.03.22 15:54
 * 黑名单组件,唯一组件,一个活动只可有一个,且序号为 1
 */
@Component
public class BlackListComponent extends BaseActComponent<BlackListComponentAttr> {

    @Override
    public Long getComponentId() {
        return ComponentId.BLACK_LIST;
    }

}
