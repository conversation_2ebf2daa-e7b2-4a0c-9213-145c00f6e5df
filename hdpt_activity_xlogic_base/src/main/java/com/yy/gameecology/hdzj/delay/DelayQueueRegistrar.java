package com.yy.gameecology.hdzj.delay;

import com.yy.gameecology.hdzj.element.ComponentAttr;

import java.util.function.BiConsumer;

/**
 * 延迟队列注册器，用于注册一个延迟队列
 * @param <T> 组件属性
 * @param <E> 自定义的延迟消息对象，尽量不要使用pb、thrift生成的对象，使用自定义的类型或基础类型都可以
 */
public interface DelayQueueRegistrar<T extends ComponentAttr, E> {

    /**
     * 注册延迟队列
     * @param key 组件中保持唯一的key
     * @param consumer 延迟消息消费者，两个参数，第一个是ComponentAttr，第二个是发布的自定义消息
     */
    void registerDelayQueue(String key, BiConsumer<T, E> consumer);
}
