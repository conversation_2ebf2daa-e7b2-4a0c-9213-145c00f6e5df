package com.yy.gameecology.hdzj.element.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.KoInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.service.EnrollmentNewService;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.ActivityKoComponentAttr;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Collections.singletonList;

/**
 * 活动Ko组件
 *
 * <AUTHOR>
 * @since 2021-10-27
 */
@Deprecated
@Component
public class ActivityKoComponent extends BaseActComponent<ActivityKoComponentAttr> {

    private static final String NAME_FORMAT = "%s:%s:%s:ko";
    private static final String SCRIPT_UPDATE_KO_DATA = "updateKo.lua";
    private static final String SCRIPT_MOVE_KO_DATA = "moveKo.lua";
    private static final String LOCK_NAME = "ko_lock";

    @Autowired
    private HdztRankingThriftClient rankingThriftClient;

    @Autowired
    protected EnrollmentNewService enrollmentService;

    @Override
    public Long getComponentId() {
        return ComponentId.PK_KO;
    }

    // @HdzjEventHandler(RankingScoreChanged.class)
    public void onRankingScoreChanged(RankingScoreChanged event, ActivityKoComponentAttr attr) {
        long actId = event.getActId();
        Date now = DateUtil.getDate(event.getOccurTime());
        if (!inKoDoingTime(attr, now)) {
            return;
        }

        List<Long> rankIds = attr.getRankIds();
        Map<Long, RankingInfo> rankingInfoMap = hdztRankingThriftClient.queryRankConfig(actId, rankIds);
        if (MapUtils.isEmpty(rankingInfoMap)) {
            return;
        }
        for (RankingInfo rankingInfo : rankingInfoMap.values()) {
            ko(rankingInfo, attr, now);
        }
    }

    // @Scheduled(cron = "0/1 * * * * ?")
    public void work() {
        timerSupport.work(LOCK_NAME, 10, this::ko);
    }

    public void ko() {
        List<ActivityInfoVo> activityInfoVos = hdztRankingThriftClient.queryEffectActInfos();
        if (CollectionUtils.isEmpty(activityInfoVos)) {
            return;
        }
        for (ActivityInfoVo activityInfoVo : activityInfoVos) {
            long actId = activityInfoVo.getActId();
            Date now = commonService.getNow(actId);
            ActivityKoComponentAttr attr = getComponentAttr(actId, 1);
            if (attr == null) {
                continue;
            }
            if (!inKoDoingTime(attr, now)) {
                continue;
            }
            Map<Long, RankingInfo> rankingInfoMap = hdztRankingThriftClient.queryRankConfig(actId, attr.getRankIds());
            if (MapUtils.isEmpty(rankingInfoMap)) {
                continue;
            }
            for (RankingInfo rankingInfo : rankingInfoMap.values()) {
                try {
                    ko(rankingInfo, attr, now);
                } catch (Exception ex) {
                    log.error("ko error,rankingInfo={}", rankingInfo, ex);
                }
            }
        }
    }

    /**
     * 本次ko结束时间早与阶段结束时间，所以积分增加由ko控制，不用严格以中台时间
     * 如果ko结算时间和阶段结束时间一样，会有临界问题
     *
     * @param rankingInfo
     * @param attr
     */
    private void ko(RankingInfo rankingInfo, ActivityKoComponentAttr attr, Date now) {

        long actId = rankingInfo.getActId();
        long rankId = rankingInfo.getRankingId();
        RankingPhaseInfo currentPhaseInfo = rankingInfo.getCurrentPhase();

        long currentPhaseId = Optional.ofNullable(currentPhaseInfo)
                .map(RankingPhaseInfo::getPhaseId).orElse(0L);
        long timeType = rankingInfo.getTimeKey();

        String endTimeSub = getEndTimeSub(attr, now);

        //更新数据
        if (attr.getKoPhaseIds().contains(currentPhaseId)) {
            Clock clock = new Clock();
            //时间key
            String timeKey = getTimeKey(now, timeType);

            long time = now.getTime();
            //查询pk数据
            PkInfo pkInfo = hdztRankingThriftClient.queryPhasePkgroup(actId, rankId, currentPhaseId, timeKey, timeKey, true, true, Maps.newHashMap());
            List<PkGroupItem> memberPkItems = Optional.ofNullable(pkInfo).map(PkInfo::getPkGroupItems).orElse(Collections.emptyList());
            Map<String, KoInfo> koInfoMap = memberPkItems.stream().map(PkGroupItem::getMemberPkItems)
                    .flatMap(Collection::stream)
                    .filter(item ->
                            CollectionUtils.isNotEmpty(item)
                                    && item.size() == 2
                                    && Math.abs(item.get(0).getScore() - item.get(1).getScore()) >= attr.getKoStartScore()
                    )
                    .map(item -> memberPkItems2KoInfo(item, time))
                    .collect(Collectors.toMap(KoInfo::getMemberKey, Function.identity()));
            clock.tag();
            String updateKey = getKey(actId, rankId, currentPhaseId, timeKey);
            if (koInfoMap.isEmpty()) {
                updateKoInfo(actId, updateKey, Maps.newHashMap(), 0L, 0L, 0, endTimeSub);
            } else {

                List<String> accomplishKoList = updateKoInfo(actId, updateKey, koInfoMap, time
                        , attr.getKoAccomplishDurationTime(), attr.getMaxKoCountPerDay(), endTimeSub);
                clock.tag();
                //发送
                for (String accomplishKo : accomplishKoList) {
                    String accomplishMember = accomplishKo.split("_")[0];
                    String seq = UUID.randomUUID().toString();
                    JSONObject dest = new JSONObject();
                    dest.put("koActId", actId);
                    dest.put("koRankId", attr.getDestRankId());
                    dest.put("koPhaseId", currentPhaseInfo.getPhaseId());

                    Map<String, String> data = ImmutableMap.of("seq", seq,
                            "member", accomplishMember,
                            "score", attr.getKoAwardScore() + "",
                            "DEST", dest.toJSONString());

                    String sign = "PW_KO_ITEM";

                    Map<String, String> responseData = hdztRankingThriftClient.invokeRetry(200, actId, data, sign, 3);
                    log.info("pk ko is completed,request:{},result:{} {}", data, responseData, clock.tag());
                    if (MapUtils.isNotEmpty(responseData)) {
                        moveKoInfo(actId, updateKey, accomplishKo);
                    } else {
                        log.error("invoke error,seq:{},type:{},data:{},sign:{},response:{} {}",
                                seq, actId, JSON.toJSONString(data), sign, responseData, clock.tag());
                    }
                }
            }
        }
    }

    private void moveKoInfo(long actId, String key, String memberKey) {
        String result = actRedisDao.executeLua(redisConfigManager.getGroupCode(actId), SCRIPT_MOVE_KO_DATA, String.class, singletonList(key),
                singletonList(memberKey));

        //返回数据处理
        JSONObject queryResultJson = JSONObject.parseObject(result);
        int code = queryResultJson.getIntValue("code");
        if (code != 0) {
            log.error("moveKoInfo error,key:{},memberKey:{},result:{}", key, memberKey, result);
        } else {
            log.info("moveKoInfo info,key:{},memberKey:{},result:{}", key, memberKey, result);
        }

    }

    /**
     * 更新ko数据并且获取完成ko的对象
     *
     * @param key
     * @param koInfoMap
     * @param nowTime
     * @param koAccomplishTime
     * @param maxKoCount
     * @return
     */
    private List<String> updateKoInfo(long actId, String key, Map<String, KoInfo> koInfoMap, long nowTime
            , long koAccomplishTime, int maxKoCount, String endTimeSub) {

        Map<String, String> dataMap = koInfoMap.entrySet().stream().collect(
                Collectors.toMap(Map.Entry::getKey, entry -> JSON.toJSONString(entry.getValue()))
        );
        String data = JSON.toJSONString(dataMap);
        //执行lua
        //完成ko的最晚开始时间
        long mixTime = nowTime - koAccomplishTime;
        //可能完成ko的最晚时间
        long possibleAccomplishKoLastTime = canAccomplishKoLastTime(new Date(nowTime), koAccomplishTime, endTimeSub);

        String groupCode = redisConfigManager.getGroupCode(actId);

        String result = actRedisDao.executeLua(groupCode, SCRIPT_UPDATE_KO_DATA, String.class, singletonList(key),
                Lists.newArrayList(data, nowTime + "", mixTime + "", maxKoCount + "",
                        possibleAccomplishKoLastTime + "", 1 + ""));

        //返回数据处理
        JSONObject queryResultJson = JSONObject.parseObject(result);
        Object dataObject = queryResultJson.get("koAccomplish");
        List<String> accomplishKoList = Lists.newArrayList();
        if (dataObject instanceof JSONArray) {
            accomplishKoList = ((JSONArray) dataObject).toJavaList(String.class);
        }
        log.info("updateKoInfo info :{}", queryResultJson);

        return accomplishKoList;
    }

    public long canAccomplishKoLastTime(Date now, long koAccomplishDurationTime, String endTimeSub) {
        return DateUtil.getDate(DateUtil.format(now, "yyyy-MM-dd " + endTimeSub)).getTime() - koAccomplishDurationTime;
    }

    /**
     * 获取key
     *
     * @param actId
     * @param rankId
     * @param phaseId
     * @param dateStr
     * @return
     */
    private String getKey(long actId, long rankId, long phaseId, String dateStr) {
        if (StringUtil.isBlank(dateStr)) {
            dateStr = "_";
        }
        String name = String.format(NAME_FORMAT, rankId, phaseId, dateStr);
        return Const.addActivityPrefix(actId, name);
    }

    /**
     * 实体转换
     *
     * @param memberPkItems
     * @param time
     * @return
     */
    private KoInfo memberPkItems2KoInfo(List<GroupMemberItem> memberPkItems, long time) {
        //左边总是的
        if (memberPkItems.get(0).getRank() > memberPkItems.get(1).getRank()) {
            Collections.reverse(memberPkItems);
        }
        GroupMemberItem winnerItem = memberPkItems.get(0);
        GroupMemberItem failItem = memberPkItems.get(1);

        String memberKey = winnerItem.getMemberId() + "_" + failItem.getMemberId();
        Long diff = winnerItem.getScore() - failItem.getScore();

        KoInfo koInfo = new KoInfo();
        koInfo.setMemberKey(memberKey);
        koInfo.setDiff(diff);
        koInfo.setEndTime(time);

        koInfo.setWinnerMemberId(winnerItem.getMemberId());
        koInfo.setWinnerScore(winnerItem.getScore());
        koInfo.setWinnerRank(winnerItem.getRank());

        koInfo.setLoserMemberId(failItem.getMemberId());
        koInfo.setLoserScore(failItem.getScore());
        koInfo.setLoserRank(failItem.getRank());

        return koInfo;
    }

    /**
     * 获取时间key，阶段榜是 "",日榜是 yyyyMMdd，小时榜是 yyyyMMddHH
     *
     * @param now
     * @param timeType
     * @return
     */
    private String getTimeKey(Date now, long timeType) {
        String timeKey = "";
        if (timeType == TimeKeyHelper.TIME_KEY_BY_DAY) {
            //日榜
            timeKey = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        } else if (timeType == TimeKeyHelper.TIME_KEY_BY_HOUR) {
            //小时榜
            timeKey = DateUtil.format(now, DateUtil.PATTERN_YYYYMMDD);
        }
        return timeKey;
    }

    /**
     * 是否处于ko时间
     *
     * @param attr
     * @param now
     * @return
     */
    public boolean inKoDoingTime(ActivityKoComponentAttr attr, Date now) {
        String endTimeSub = getEndTimeSub(attr, now);
        Date startTime = DateUtil.getDate(DateUtil.format(now, "yyyy-MM-dd " + attr.getStartTimeSub()));
        Date endTime = DateUtil.getDate(DateUtil.format(now, "yyyy-MM-dd " + endTimeSub));
        Date startDay = DateUtil.getDate(attr.getStartDay() + " " + attr.getStartTimeSub(), DateUtil.DEFAULT_PATTERN);
        Date endDay = DateUtil.getDate(attr.getEndDay() + " " + endTimeSub, DateUtil.DEFAULT_PATTERN);
        if (startDay.after(now) || endDay.before(now) || startTime.after(now) || endTime.before(now)) {
            return false;
        }

        return true;
    }

    public String getEndTimeSub(ActivityKoComponentAttr attr, Date now) {
        String endTimeSub = attr.getEndTimeSubMap().get(DateUtil.format(now, DateUtil.PATTERN_TYPE5));
        if (StringUtil.isEmpty(endTimeSub)) {
            endTimeSub = "22:00:00";
        }

        return endTimeSub;
    }
}
