package com.yy.gameecology.hdzj;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.gameecology.activity.bean.ChannelInfo;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.client.thrift.FtsBaseInfoBridgeClient;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.client.thrift.ZhuiWanPrizeIssueServiceClient;
import com.yy.gameecology.activity.commons.AwardIssueHelper;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.exception.GameEcologyBusinessException;
import com.yy.gameecology.activity.processor.ranking.impl.RankingAnyProcessor;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.activity.worker.timer.TimerSupport;
import com.yy.gameecology.activity.worker.web.BaseController;
import com.yy.gameecology.common.bean.UserCurrentChannel;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.model.gameecology.HdzjActivity;
import com.yy.gameecology.common.db.model.gameecology.HdzjComponent;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.redisson.RedissonLocker;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.hdzj.bean.AwardBean;
import com.yy.gameecology.hdzj.bean.ElementBaseInfo;
import com.yy.gameecology.hdzj.bean.PKMembers;
import com.yy.gameecology.hdzj.delay.ComponentDelayQueue;
import com.yy.gameecology.hdzj.delay.DelayQueueEventPublisher;
import com.yy.gameecology.hdzj.delay.DelayQueueRegistrar;
import com.yy.gameecology.hdzj.element.ActComponent;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanNameAware;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.RequestMapping;

import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * 提供活动组件的公共实现
 *
 * @author: 郭立平[<EMAIL>]
 * @date: 2021/2/24 16:49
 **/
public abstract class BaseActComponent<T extends ComponentAttr> extends BaseController
        implements ActComponent<T>, BeanNameAware, DelayQueueRegistrar<T, Object>, DelayQueueEventPublisher<T, Object> {

    protected final Logger log = LoggerFactory.getLogger(this.getClass());

    private String beanName;

    public static final String COMPONENT_KEY_TPL = Const.COMPONENT_KEY_SUFFIX + ":%s:%s:%s";

    // 奖励发放 redis list key， 用于削峰、出错重试 等
    public static final String BASE_AWARD_ISSUE_KEY = "base_award_issue";

    @Autowired
    protected CacheService cacheService;

    @Autowired
    protected ActRedisGroupDao actRedisDao;

    @Autowired
    protected RedisConfigManager redisConfigManager;

    @Autowired
    protected GameecologyDao gameecologyDao;

    @Autowired
    protected HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    protected HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    protected TimerSupport timerSupport;

    @Autowired
    protected SvcSDKService svcSDKService;

    @Autowired
    protected CommonService commonService;

    @Autowired
    protected RankingAnyProcessor rankingAnyProcessor;

    @Autowired
    protected HdztRankGenRoleService hdztRankGenRoleService;

    @Autowired
    protected Locker locker;

    @Autowired
    protected RedissonLocker redissonLocker;

    @Autowired
    protected ActInfoService actInfoService;

    @Autowired
    protected ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    @Autowired
    protected BigDataService bigDataService;

    @Autowired
    protected FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Autowired
    protected BroadCastHelpService broadCastHelpService;

    @Autowired
    protected HdztAwardService hdztAwardService;

    @Autowired
    protected HdztRankService hdztRankService;

    @Autowired
    protected AwardIssueHelper awardIssueHelper;

    @Autowired
    protected OnMicService onMicService;

    @Autowired
    protected EnrollmentNewService enrollmentService;

    @Autowired
    private ComponentDelayQueue componentDelayQueue;

    @Autowired
    protected HdztActorInfoService hdztActorInfoService;

    @Value("${component.log.detail:false}")
    private boolean logDetail;

    public BaseActComponent() {
    }

    @Override
    public boolean isUniq1UseIndex() {
        return false;
    }

    @Override
    public Class<T> getMyAttrClass() {
        ParameterizedType ptype = (ParameterizedType) this.getClass().getGenericSuperclass();
        return (Class<T>) ptype.getActualTypeArguments()[0];
    }

    /**
     * 活动是否配置了本组件
     */
    public boolean isMyDuty(long actId) {
        return getActivityIds().contains(actId);
    }

    @Override
    public Set<Long> getActivityIds() {
        Set<Long> set = Sets.newLinkedHashSet();
        Long cmptId = this.getComponentId();
        if (cmptId != null) {
            Map<Long, HdzjActivity> activityMap = cacheService.getHdzjActivityMap();
            for (Long actId : activityMap.keySet()) {
                // 1.看活动是否引用了组件
                var components = cacheService.getHdzjComponentMap(actId, cmptId);
                if (MapUtils.isEmpty(components)) {
                    continue;
                }

                // 2.分组匹配验证：看能否在指定分组中台找到活动信息
                if (!SysEvHelper.isLocal()) {
                    ActivityInfoVo activityInfoVo = hdztRankingThriftClient.queryActivityInfo(actId);
                    if (activityInfoVo == null) {
                        continue;
                    }
                }

                // 3.遍历每个组件，找到和自身组件id相同的
                for (var entry : components.entrySet()) {
                    if (entry.getValue() != null && Const.isOk1(entry.getValue().getStatus())) {
                        set.add(actId);
                        break;
                    }
                }
            }
        }
        return set;
    }

    /**
     *
     * 获取配置了本组件且有效状态的活动
     */
    public Set<Long> getComponentEffectActIds(){
        Set<Long> actIds = getActivityIds();
        log.info("getComponentEffectActIds actIds:{}", JSON.toJSON(actIds));
        if(CollectionUtils.isEmpty(actIds)){
            return actIds;
        }
        List<ActivityInfoVo> effectActInfos = hdztRankingThriftClient.queryEffectActInfos();
        if (CollectionUtils.isEmpty(effectActInfos)) {
            return Sets.newHashSet();
        }
        List<Long> effectActIds = effectActInfos.stream().map(ActivityInfoVo::getActId).collect(Collectors.toList());
        Collections.shuffle(effectActIds);

        return effectActIds.stream().filter(actIds::contains).collect(Collectors.toSet());
    }

    @Override
    public HdzjComponent getHdzjComponent(long actId, long cmptUseInx) {
        return cacheService.getHdzjComponent(actId, this.getComponentId(), cmptUseInx);
    }

    @Override
    public T getComponentAttr(long actId, long cmptUseInx) {
        Clock clock = new Clock();
        long cmptId = this.getComponentId();
        try {
            // 提取有效的属性，形成json子串
            Class<T> clazz = this.getMyAttrClass();
            HdzjComponent hdzjComponent = cacheService.getHdzjComponent(actId, cmptId, cmptUseInx);
            if (hdzjComponent == null) {
                return null;
            }

            T attr = cacheService.getComponentAttr(actId, cmptId, cmptUseInx);
            if (attr == null) {
                return cacheService.getComponentAttrFromDb(actId, cmptId, cmptUseInx, getMyAttrClass(), hdzjComponent);
            }

            return attr;
        } catch (Throwable t) {
            log.error("getComponentAttr exception@actId:{}, cmptId:{}, cmptUseInx:{}, err:{} {}",
                    actId, cmptId, cmptUseInx, t.getMessage(), clock.tag(), t);
            return null;
        }
    }

    @Override
    public T getUniqueComponentAttr(long actId) {
        // 1. 若是唯一 1 使用序号，则定向查找返回
        if (this.isUniq1UseIndex()) {
            return getComponentAttr(actId, 1);
        }

        // 2. 否则找出的所有使用序号若不是刚好为1个，也返回 null
        long cmptId = this.getComponentId();
        Map<Long, HdzjComponent> map = cacheService.getHdzjComponentMap(actId, cmptId);
        if (map.size() != 1) {
            log.warn("getUniqueComponentAttr error,size :{}", map.size());
            return null;
        }

        // 3. 使用确定的唯一一个使用序号，查找组件属性对象
        long cmptUseInx = map.entrySet().iterator().next().getKey();
        return getComponentAttr(actId, cmptUseInx);
    }

    @Override
    public T tryGetUniqueComponentAttr(long actId) {
        // 1. 若是唯一 1 使用序号，则定向查找返回
        if (this.isUniq1UseIndex()) {
            return getComponentAttr(actId, 1);
        }

        // 2. 否则找出的所有使用序号若不是刚好为1个，也返回 null
        long cmptId = this.getComponentId();
        Map<Long, HdzjComponent> map = cacheService.getHdzjComponentMap(actId, cmptId);
        if (map.size() != 1) {
            log.warn("tryGetUniqueComponentAttr,size :{}", map.size());
            return null;
        }

        // 3. 使用确定的唯一一个使用序号，查找组件属性对象
        long cmptUseInx = map.entrySet().iterator().next().getKey();
        return getComponentAttr(actId, cmptUseInx);
    }

    @Override
    public List<T> getAllComponentAttrs(long actId) {
        long cmptId = this.getComponentId();
        var components = cacheService.getHdzjComponentMap(actId, cmptId);
        if (MapUtils.isEmpty(components)) {
            return Collections.emptyList();
        }

        return components.values().stream()
                .filter(Objects::nonNull)
                .filter(component -> Const.isOk1(component.getStatus()))
                .map(component -> getComponentAttr(actId, component.getCmptUseInx()))
                .toList();
    }

    @Override
    public String makeKey(ComponentAttr componentAttr, String name) {
        long actId = componentAttr.getActId();
        long cmptId = componentAttr.getCmptId();
        long cmptUseInx = componentAttr.getCmptUseInx();
        return makeKey(actId, cmptId, cmptUseInx, name);
    }

    @Override
    public String makeKey(ComponentRequest<T> request, String name) {
        return makeKey(request.getComponentAttr(), name);
    }

    @Override
    public String makeKey(long actId, long cmptUseInx, String name) {
        return makeKey(actId, this.getComponentId(), cmptUseInx, name);
    }

    public static String makeKey(long actId, long cmptId, long cmptUseInx, String name) {
        String subKey = String.format(COMPONENT_KEY_TPL, cmptId, cmptUseInx, name);
        return Const.addActivityPrefix(actId, subKey);
    }

    @Override
    public Map<String, Object> getUiConfig(ComponentRequest<T> request) {
        ComponentAttr componentAttr = request.getComponentAttr();
        long actId = componentAttr.getActId();
        Long componentId = this.getComponentId();
        long cmptUseInx = componentAttr.getCmptUseInx();
        Map<String, String> map = cacheService.getHdzjComponentUiMap(actId, componentId, cmptUseInx);
        return Maps.newLinkedHashMap(map);
    }

    @Override
    public ElementBaseInfo getElementBaseInfo(ComponentRequest<T> request) {
        ComponentAttr componentAttr = request.getComponentAttr();
        ElementBaseInfo info = null;
        long cmptId = this.getComponentId();
        HdzjComponent component = cacheService.getHdzjComponent(componentAttr.getActId(), cmptId, componentAttr.getCmptUseInx());
        if (component != null) {
            info = new ElementBaseInfo();
            info.setTitle(component.getCmptTitle());
            info.setRemark(component.getRemark());
            info.setWeight(component.getShowOrder());
        }
        return info;
    }

    @Override
    public Map<String, Object> getBusinessData(ComponentRequest<T> request) {
        return null;
    }

    @Override
    public Map<String, Object> operate(ComponentRequest<T> request) {
        return null;
    }

    protected String getRedisGroupCode(long actId) {
        return redisConfigManager.getGroupCode(actId);
    }

    protected String getFixRedisGroupCode(long actId) {
        return redisConfigManager.getGroupCode(actId, true);
    }

    protected List<PKMembers<Rank>> setUpPK(long actId, long rankId, long phaseId, List<Rank> ranks) {
        PkInfo pkInfo = hdztRankingThriftClient.queryPhasePkgroup(actId, rankId, phaseId, "");
        Map<String, Rank> members = new HashMap<>(ranks.size());
        for (Rank rank : ranks) {
            members.put(rank.getMember(), rank);
        }
        List<PKMembers<Rank>> pkRankItems = new ArrayList<>();
        List<PkGroupItem> pkGroupItems = pkInfo.getPkGroupItems();
        Comparator<Rank> comparator = Comparator.comparingLong(Rank::getRank);
        for (PkGroupItem pkGroupItem : pkGroupItems) {
            for (List<GroupMemberItem> memberPkItem : pkGroupItem.getMemberPkItems()) {
                String memberId1 = memberPkItem.get(0).getMemberId();
                String memberId2 = memberPkItem.get(1).getMemberId();
                pkRankItems.add(new PKMembers<>(members.get(memberId1), members.get(memberId2), comparator));
            }
        }
        return pkRankItems;
    }

    /**
     * 判断活动是否结束时间超过了 minutes 分钟， 一般用来判断是否执行活动相关逻辑。为防止临界，这里特意使用分钟单位
     *
     * @param actId   - 要判断的活动
     * @param minutes - 活动结束超过的分钟数
     * @return true：明确已经超过， false：明确没有超过
     * @throws Exception - 活动查询失败时，不能确定活动是否结束，保险起见抛出异常，由调用者决定如何处理
     */
    protected boolean isActivityOver(long actId, int minutes) throws Exception {
        ActivityInfoVo avo = hdztRankingThriftClient.queryActivityCacheNoTime(actId);
        if (avo == null) {
            throw new GameEcologyBusinessException("活动查询失败", 40011);
        }
        long nowMinutes = commonService.getNow(actId).getTime() / 1000 / 60;
        long endMinutes = avo.getEndTime() / 1000 / 60;
        return nowMinutes - endMinutes > minutes;
    }

    /**
     * 保存奖励（配合 giveAwards() 才能工作）
     * 本函数将要发放的奖励存到 redis 队列中，由外部主动调用 giveAwards() 进行发放
     *
     * @param attr   - 用于生成奖励存放 redis key
     * @param seq    - 仅用于日志，方便跟踪调用链条
     * @param awards - 要入队的奖励，AwardBean.seq 会用作幂等防重，所以本函数对重复发奖天然免疫
     */
    public void saveAward(ComponentAttr attr, String seq, List<AwardBean> awards) {
        String listKey = makeKey(attr, BASE_AWARD_ISSUE_KEY);
        awardIssueHelper.saveAward(attr.getActId(), seq, listKey, awards);
    }

    /**
     * 奖励发放（配合 saveAward() 才能工作）
     * 注意：1）需要有外部定时器持续驱动，否则队列发空后，就结束退出（这样设计的原因是因为重试频度等需要外部根据实际情况来控制）
     * 2）因为一次调用就会尝试将队列发空，可能耗时比较长，对此阻塞敏感的场合，应异步发起本函数的调用
     *
     * @param retry             - 单次发放动作重试次数，总共做 retry + 1 次尝试（用于多服务实例时 failover，单服务实例时即刻重试一般是没有用的）
     * @param issueRetrySeconds - 发放重试秒数
     * @param actOverMinutes    - 活动结束分钟数（活动结束超过这个值的将不再处理）
     */
    protected void giveAwards(int retry, int issueRetrySeconds, int actOverMinutes) {
        Clock clock = new Clock();
        Set<Long> activityIds = this.getActivityIds();
        for (Long actId : activityIds) {
            // 活动结束已经超过 actOverMinutes 分钟的不再处理
            try {
                if (this.isActivityOver(actId, actOverMinutes)) {
                    continue;
                }
            } catch (Exception e) {
                log.error("giveAwards exception@isActivityOver fail, actId:{}, err:", actId, e);
                continue;
            }

            String groupCode = redisConfigManager.getGroupCode(actId);
            List<ComponentAttr> attrs = (List<ComponentAttr>) this.getAllComponentAttrs(actId);
            for (ComponentAttr attr : attrs) {
                String listKey = makeKey(attr, BASE_AWARD_ISSUE_KEY);

                // 记录队列当前长度, 若为0直接结束循环
                long len = actRedisDao.llen(groupCode, listKey);
                if (len == 0) {
                    continue;
                }

                long total = len;
                String content = null;
                while ((content = actRedisDao.lpop(groupCode, listKey)) != null) {
                    awardIssueHelper.giveOneAward(groupCode, listKey, content, issueRetrySeconds, retry);
                    // 因为giveOneAward中会将失败的重新入队，这里用来防止无限循环，多机情况下会有一些冗余尝试，影响不大
                    if (--len < 0) {
                        log.warn("giveAwards break@key:{}, total:{}, len:{}", listKey, total, len);
                        break;
                    }
                }
            }
        }
        log.info("giveAwards Scheduled done@act size:{} {}", activityIds.size(), clock.tag());
    }

    protected String buildActRuliuMsg(long actId, boolean error, String title, String msg) {
        return commonService.buildActRuliuMsg(actId, error, title, msg);
    }

    protected boolean log() {
        return !SysEvHelper.isDeploy() || logDetail;
    }

    @Override
    public void registerDelayQueue(String key, BiConsumer<T, Object> consumer) {
        if (SysEvHelper.isLocal() || SysEvHelper.isHistory()) {
            return;
        }

        componentDelayQueue.register(this, key, consumer);
    }

    @Override
    public void publishDelayEvent(T attr, String key, Object event, long expiredMills) {
        componentDelayQueue.publishDelayEvent(attr.getActId(), getComponentId(), attr.getCmptUseInx(), key, event, expiredMills);
    }

    public StringRedisTemplate getFixRedisTemplate(long actId) {
        return actRedisDao.getRedisTemplate(getFixRedisGroupCode(actId));
    }

    @RequestMapping("forward/cast")
    public Response<Long> forwardCast(long actId, int cmptIndex, String noticeType, String noticeValue, String noticeMsg) {
        T attr = getComponentAttr(actId, cmptIndex);
        if (attr == null) {
            return Response.fail(400, "component not exist!");
        }

        boolean inActTime = actInfoService.inActTime(actId);
        if (!inActTime) {
            return Response.fail(500, "not in activity time!");
        }

        long uid = getLoginYYUid();
        if (uid <= 0) {
            return Response.fail(401, "login is need!");
        }

        GameecologyActivity.CommonNoticeResponse.Builder panel = GameecologyActivity.CommonNoticeResponse.newBuilder()
                .setActId(attr.getActId())
                .setNoticeType(noticeType)
                .setNoticeValue(noticeValue)
                .setNoticeMsg(noticeMsg);

        GameecologyActivity.GameEcologyMsg msg = GameecologyActivity.GameEcologyMsg.newBuilder()
                .setUri(GameecologyActivity.PacketType.CommonNoticeResponseUri_VALUE)
                .setCommonNoticeResponse(panel).build();

        svcSDKService.unicastUid(uid, msg);
        log.info("forwardCast with uid:{} {} {} {}", uid, noticeType, noticeValue, noticeMsg);
        return Response.success(uid);
    }

    /**
     *
     * 优先从事件提取频道信息--->读取不到，读取用户当前频道
     */
    protected ChannelInfo resolveUserChannel(long uid, RankingScoreChanged event) {
        ChannelInfo channelInfo = new ChannelInfo();
        //优先从事件提取频道信息
        if (MapUtils.isNotEmpty(event.getActors())) {
            for (Long roleId : event.getActors().keySet()) {
                HdztActorInfo hdztActorInfo = hdztActorInfoService.getHdztActorInfo(roleId);
                if (hdztActorInfo == null) {
                    log.error("can not found role config,roleId:{}", roleId);
                    continue;
                }
                if (hdztActorInfo.getType() == RoleType.HALL.getValue()) {
                    String member = event.getActors().get(roleId);
                    String[] ssid = member.split("_");
                    return new ChannelInfo(Convert.toLong(ssid[0]), Convert.toLong(ssid[1]));
                }
            }
        }
        //读取不到，读取用户当前频道
        UserCurrentChannel userCurrentChannel = commonService.getNoCacheUserCurrentChannel(uid);
        if (userCurrentChannel != null) {
            return new ChannelInfo(userCurrentChannel.getTopsid(), userCurrentChannel.getSubsid());
        }

        log.warn("can not result user channel,uid:{},actor:{}", uid, JSON.toJSONString(event.getActors()));
        return channelInfo;
    }



    @Override
    public void setBeanName(String name) {
        this.beanName = name;
    }

    @Override
    public String getBeanName() {
        return this.beanName;
    }

    public RedissonClient getRedissonClient() {
        return redisConfigManager.getRedissonClient();
    }
}
