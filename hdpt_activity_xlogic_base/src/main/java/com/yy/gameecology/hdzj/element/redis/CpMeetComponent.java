package com.yy.gameecology.hdzj.element.redis;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.StrPool;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yy.boot.starter.util.JsonUtils;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.annotation.UseRedisStore;
import com.yy.gameecology.activity.bean.ChannelChatTextInnerEvent;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserEnterTemplateEvent;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.event.*;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.hdzt.TaskProgressChanged;
import com.yy.gameecology.activity.service.*;
import com.yy.gameecology.common.bean.*;
import com.yy.gameecology.common.client.WebdbThriftClient;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.FstAppBroadcastType;
import com.yy.gameecology.common.consts.GeParamName;
import com.yy.gameecology.common.utils.*;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpMeetComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.BannerSvgaTextConfig;
import com.yy.java.webdb.BatchUserInfoWithNickExt;
import com.yy.java.webdb.WebdbUserInfo;
import com.yy.thrift.hdztaward.AwardModelInfo;
import com.yy.thrift.hdztaward.BatchLotteryResult;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.thrift.hdztranking.*;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@UseRedisStore
@RequestMapping("/cpMeet")
@RestController
@Component
public class CpMeetComponent extends BaseActComponent<CpMeetComponentAttr> {

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private WebdbThriftClient webdbThriftClient;

    @Autowired
    private BaiduInfoFlowRobotService baiduInfoFlowRobotService;

    @Autowired
    private UserInfoService userInfoService;

    private static final long CP_BRO_BANNER_ID = 5098001L;

    private static final long CP_BRO_LAYOUT_BANNER_ID = 5098002L;

    private static final long CP_BRO_LOTTERY_BANNER_ID = 5098003L;

    private static final String RETRY_SEQ = "retry_seq";

    private static final String RETRY_DAILY_SEQ = "retry_daily_seq";

    private static final String SCORE_CHANGE_POP = "score_change_pop:";

    private static final String MISSION_FINISH_LIST = "mission_finish_list";

    private static final String MISSION_MAX_FINISH_ZSET = "mission_max_finish_zset";

    private static final String LOTTERY_INFO_FORMAT = "lottery_channel_%d_%d";

    private static final String LOTTERY_KEY_FORMAT = "lottery_%d_%d_%d";
    private static final String LOTTERY_TIP_FORMAT = "lottery_tip_%s_%d";
    private static final String LOTTERY_SEQ_FORMAT = "lottery_%s_%d";

    private static final String BANNER_SHOW_INFO_FORMAT = "show_channel_%d_%d";
    private static final String USER_AWARD_POP_KEY_FORMAT = "award_pop_%d";

    private static final String DELAY_AWARD_POP = "cp_meet_delay_award_pop";

    private static final String STATIC_INFO_FORMAT = "static_%s_%d_%d";
    private static final int DAILY_TP = 1;
    private static final int MISSION_TP = 2;

    @PostConstruct
    @NeedRecycle(notRecycle = true, author = "chenxiazhuan")
    public void init() throws Exception {
        registerDelayQueue(DELAY_AWARD_POP, this::doDelayPopAward);
    }

    /**
     * 每小时一次日报
     */
    @NeedRecycle(author = "chenxiazhuan", notRecycle = true)
    @Scheduled(cron = "0 1 0/1 * * ?  ")
    public void staticReport() {
        Set<Long> actIds = this.getComponentEffectActIds();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(actIds)) {
            log.info("staticReport actIds is empty");
            return;
        }
        for (Long actId : actIds) {
            if (!actInfoService.inActTime(actId)) {
                log.info("staticReport actId not  inActTime ,actId:{}",actId);
                return;
            }
            CpMeetComponentAttr attr = tryGetUniqueComponentAttr(actId);
            if (attr == null) {
                log.warn("staticReport attr is  null ,actId:{}",actId);
                continue;
            }

            Date now = commonService.getNow(attr.getActId());
            Date noticeDate = DateUtil.addHours(now, -1);

            String timeCode = DateUtil.format(noticeDate, DateUtil.PATTERN_TYPE7);
            String execKey = makeKey(attr, "execCpMeetStatic:" + timeCode);
            if (!actRedisDao.setNX(getRedisGroupCode(actId), execKey, StringUtil.ONE)) {
                log.info("staticReport has report execKey:{}", execKey);
                return;
            }

            log.info("begin staticReport game,actId:{},now:{}", actId,now);

            doStaticReport(actId, attr,noticeDate);

        }
    }


    @GetMapping("/taskStatic")
    public Response<String> taskStatic(Long actId) {
        Date now = commonService.getNow(actId);
        Date noticeDate = DateUtil.addHours(now, -1);
        doStaticReport(actId, getUniqueComponentAttr(actId), noticeDate);
        return Response.ok();
    }

    /**
     * 当前用户所关联的CP对象列表
     *
     * @param request
     * @param response
     * @param actId
     * @param cmptId
     * @return
     */
    @RequestMapping("/getCpList")
    public Response getCpList(HttpServletRequest request, HttpServletResponse response, long actId, long cmptId) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(400, "未登陆");
        }
        List<CpMember> cpMembers = new ArrayList<>();
        CpMeetComponentAttr componentAttr = getComponentAttr(actId, cmptId);
        if (componentAttr == null) {
            return Response.fail(400, "component not exist");
        }
        Map<String, QueryRankingRequest> reqMap = Maps.newHashMap();
        //用户对主播的贡献榜单，当前用户是主播身份
        QueryRankingRequest contributeReq = new QueryRankingRequest();
        contributeReq.setActId(actId);
        contributeReq.setRankingId(componentAttr.getCpContributeRankId());
        contributeReq.setPhaseId(componentAttr.getPhaseId());
        contributeReq.setFindSrcMember(Convert.toString(uid));
        contributeReq.setRankingCount(componentAttr.getRankLimit());
        reqMap.put("contribute", contributeReq);
        //主播对用户的贡献榜单，当前用户是神豪身份
        QueryRankingRequest antiContributeReq = new QueryRankingRequest();
        antiContributeReq.setActId(actId);
        antiContributeReq.setRankingId(componentAttr.getCpAntiContributeRankId());
        antiContributeReq.setPhaseId(componentAttr.getPhaseId());
        antiContributeReq.setFindSrcMember(Convert.toString(uid));
        antiContributeReq.setRankingCount(componentAttr.getRankLimit());
        reqMap.put("antiContribute", antiContributeReq);
        Map<String, BatchRankingItem> rankMap = hdztRankingThriftClient.queryBatchRanking(reqMap, null);

        Set<Long> uids = Sets.newHashSet(uid);
        //神豪|主播
        Set<String> cpMemberList = Sets.newLinkedHashSet();
        List<ActorQueryItem> queryCpScorePara = Lists.newArrayList();
        //用户对主播的贡献榜单，当前用户是主播身份
        for (Rank rank : rankMap.get("contribute").getData()) {
            long toUid = Convert.toLong(rank.getMember());
            uids.add(toUid);
            String cpMemberStr = toUid + "|" + uid;
            cpMemberList.add(cpMemberStr);

            ActorQueryItem queryItem = new ActorQueryItem();
            queryItem.setActorId(cpMemberStr);
            queryItem.setRankingId(componentAttr.getCpRankId());
            queryItem.setPhaseId(componentAttr.getPhaseId());
            queryItem.setWithStatus(false);
            queryCpScorePara.add(queryItem);
        }
        //主播对用户的贡献榜单，当前用户是神豪身份
        for (Rank rank : rankMap.get("antiContribute").getData()) {
            long toUid = Convert.toLong(rank.getMember());
            uids.add(toUid);
            String cpMemberStr = uid + "|" + toUid;
            cpMemberList.add(cpMemberStr);

            ActorQueryItem queryItem = new ActorQueryItem();
            queryItem.setActorId(cpMemberStr);
            queryItem.setRankingId(componentAttr.getCpRankId());
            queryItem.setPhaseId(componentAttr.getPhaseId());
            queryItem.setWithStatus(false);
            queryCpScorePara.add(queryItem);
        }
        List<ActorInfoItem> actorInfoItems = queryCpScorePara.isEmpty() ? List.of() : hdztRankingThriftClient.queryActorRankingInfo(actId, queryCpScorePara);
        Map<String, Long> scoreMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(actorInfoItems)) {
            for (ActorInfoItem actorInfoItem : actorInfoItems) {
                scoreMap.put(actorInfoItem.getActorId(), actorInfoItem.getScore());
            }
        }
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(Lists.newArrayList(uids));
        Map<String, MemInfo> userInfoMap = batched.getUserInfoMap().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i -> toMemInfo(i.getValue())));


        for (String cpMemberStr : cpMemberList) {
            String[] member = cpMemberStr.split("\\|");
            CpMember cpMember = new CpMember();
            cpMember.setCpMember(cpMemberStr);
            cpMember.setUser(userInfoMap.get(member[0]));
            cpMember.setAnchor(userInfoMap.get(member[1]));
            cpMember.setScore(scoreMap.getOrDefault(cpMemberStr, 0L));
            cpMembers.add(cpMember);
        }

        NickExt nickExt = JsonUtils.deserialize(batched.getNickExt(), NickExt.class);

        CpListRsp rsp = new CpListRsp();
        rsp.setCpMembers(cpMembers);
        rsp.setNickExtUsers(nickExt.getUsers());
        return Response.success(rsp);
    }

    /**
     * 获取任务详情
     *
     * @param request
     * @param response
     * @param cpMember
     * @param actId
     * @param cmptId
     * @return
     */
    @RequestMapping("/getCpMissions")
    public Response getCpMissions(HttpServletRequest request, HttpServletResponse response, String cpMember, long actId, long cmptId) {
        long uid = getLoginYYUid(request, response);
        if (uid <= 0) {
            return Response.fail(400, "未登陆");
        }
        CpMeetComponentAttr componentAttr = getComponentAttr(actId, cmptId);
        if (componentAttr == null) {
            return Response.fail(400, "component not exist");
        }
        CpMission cpMission = new CpMission();

        if (!StringUtil.isEmpty(cpMember)) {
            List<ActorQueryItem> queryCpScorePara = Lists.newArrayList();
            ActorQueryItem queryItem = new ActorQueryItem();
            ActorQueryItem dailyQueryItem = new ActorQueryItem();
            queryItem.setActorId(cpMember);

            queryItem.setRankingId(componentAttr.getCpRankId());
            queryItem.setPhaseId(componentAttr.getPhaseId());
            queryItem.setWithStatus(false);
            queryCpScorePara.add(queryItem);

            dailyQueryItem.setRankingId(componentAttr.getCpDailyRankId());
            dailyQueryItem.setPhaseId(componentAttr.getPhaseId());
            dailyQueryItem.setActorId(cpMember);
            dailyQueryItem.setWithStatus(false);
            String dateKey = DateUtil.format(commonService.getNow(actId), "yyyyMMdd");
            dailyQueryItem.setDateStr(dateKey);
            queryCpScorePara.add(dailyQueryItem);

            List<ActorInfoItem> actorInfoItems = hdztRankingThriftClient.queryActorRankingInfo(actId, queryCpScorePara);
            if (!CollectionUtils.isEmpty(actorInfoItems)) {
                ActorInfoItem actorInfoItem = actorInfoItems.get(0);
                cpMission.setTotalScore(actorInfoItem.getScore());
                if (actorInfoItems.size() > 1) {
                    ActorInfoItem actorInfoDailyItem = actorInfoItems.get(1);
                    cpMission.setDailyScore(actorInfoDailyItem.getScore());
                }
            }
        }

        List<Mission> missions = componentAttr.getMissionMap().entrySet().stream().map(item -> {
            long missionVal = item.getValue().getGoalScore();
            Mission mission = new Mission();
            mission.setTaskId(item.getKey());
            mission.setScore(missionVal);
            mission.setFinish(missionVal <= cpMission.getTotalScore());
            return mission;
        }).collect(Collectors.toList());

        List<Mission> dailyMissions = componentAttr.getDailyMissionMap().entrySet().stream().map(item -> {
            long dailyMissionVal = item.getValue().getGoalScore();
            Mission mission = new Mission();
            mission.setTaskId(item.getKey());
            mission.setScore(dailyMissionVal);
            mission.setFinish(dailyMissionVal <= cpMission.getDailyScore());
            return mission;
        }).collect(Collectors.toList());


        cpMission.setMissions(missions);
        cpMission.setDailyMissions(dailyMissions);

        if (StringUtils.isBlank(cpMember)){
            UserBaseInfo userInfo = commonService.getUserInfo(uid, false);
            cpMission.setAvatar(userInfo.getHdLogo());
            cpMission.setNick(userInfo.getNick());
        }
        return Response.success(cpMission);
    }

    @RequestMapping("/getScrollCpList")
    public Response getScrollCpList(@RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptId) {

        CpMeetComponentAttr attr = getComponentAttr(actId, cmptId);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }
        List<String> taskStringList = actRedisDao.lrange(getRedisGroupCode(actId), makeKey(attr, MISSION_FINISH_LIST), -30, -1);
        if (CollectionUtils.isEmpty(taskStringList)) {
            return Response.success(List.of());
        }
        Set<Long> uids = Sets.newHashSet();
        LinkedList<CpTaskFinalInfo> taskFinalList = Lists.newLinkedList();
        for (String taskString : taskStringList) {
            CpTaskFinalInfo info = JsonUtils.deserialize(taskString, CpTaskFinalInfo.class);
            taskFinalList.addFirst(info);
            uids.add(info.getUserUid());
            uids.add(info.getAnchorUid());
        }
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(Lists.newArrayList(uids));
        Map<Long, MemInfo> userInfoMap = batched.getUserInfoMap().entrySet().stream()
                .collect(Collectors.toMap(i -> Long.parseLong(i.getKey()), i -> toMemInfo(i.getValue())));


        List<ScrollCpInfo> cpInfos = Lists.newArrayList();
        for (CpTaskFinalInfo cpTaskFinalInfo : taskFinalList) {
            ScrollCpInfo scrollCpInfo = new ScrollCpInfo();
            scrollCpInfo.setLevel(cpTaskFinalInfo.getLevel());
            scrollCpInfo.setLevelName(StringUtils.trimToEmpty(attr.getMissionMap().get(cpTaskFinalInfo.getLevel()).getTaskName()));
            scrollCpInfo.setAnchor(userInfoMap.get(cpTaskFinalInfo.getAnchorUid()));
            scrollCpInfo.setUser(userInfoMap.get(cpTaskFinalInfo.getUserUid()));
            scrollCpInfo.setSid(cpTaskFinalInfo.getSid());
            scrollCpInfo.setSsid(cpTaskFinalInfo.getSsid());
            cpInfos.add(scrollCpInfo);
        }

        NickExt nickExt = JsonUtils.deserialize(batched.getNickExt(), NickExt.class);
        ScrollCpInfoRep rsp = new ScrollCpInfoRep();
        rsp.setList(cpInfos);
        rsp.setNickExtUsers(nickExt.getUsers());

        log.info("getScrollCpList size:{}", cpInfos.size());
        return Response.success(rsp);
    }

    /**
     * 相会成就，完成累计任务Lv4
     *
     * @param actId
     * @param cmptId
     * @return
     */
    @RequestMapping("/getMeetAchievement")
    public Response getMeetAchievement(@RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptId) {

        CpMeetComponentAttr attr = getComponentAttr(actId, cmptId);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }

        Set<String> taskStringList = actRedisDao.getRedisTemplate(getRedisGroupCode(actId)).opsForZSet().range(makeKey(attr, MISSION_MAX_FINISH_ZSET), 0, 99);
        if (CollectionUtils.isEmpty(taskStringList)) {
            return Response.success(List.of());
        }
        Set<Long> uids = Sets.newHashSet();
        LinkedList<CpTaskFinalInfo> taskFinalList = Lists.newLinkedList();
        for (String taskString : taskStringList) {
            CpTaskFinalInfo info = JsonUtils.deserialize(taskString, CpTaskFinalInfo.class);
            taskFinalList.addFirst(info);
            uids.add(info.getUserUid());
            uids.add(info.getAnchorUid());
        }

        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(Lists.newArrayList(uids));
        Map<String, MemInfo> userInfoMap = batched.getUserInfoMap().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, i -> toMemInfo(i.getValue())));
        List<CpAchievementMember> cpMembers = Lists.newArrayList();
        for (CpTaskFinalInfo cpTaskFinalInfo : taskFinalList) {
            CpAchievementMember achievementMember = new CpAchievementMember();
            achievementMember.setCpMember(cpTaskFinalInfo.getUserUid() + "|" + cpTaskFinalInfo.getAnchorUid());
            achievementMember.setAnchor(userInfoMap.get(String.valueOf(cpTaskFinalInfo.getAnchorUid())));
            achievementMember.setUser(userInfoMap.get(String.valueOf(cpTaskFinalInfo.getUserUid())));
            achievementMember.setFinishTime(cpTaskFinalInfo.getFinishTime());
            cpMembers.add(achievementMember);
        }


        NickExt nickExt = JsonUtils.deserialize(batched.getNickExt(), NickExt.class);
        CpAchievementRep rsp = new CpAchievementRep();
        rsp.setCpMembers(cpMembers);
        rsp.setNickExtUsers(nickExt.getUsers());
        log.info("getMeetAchievement size:{}", cpMembers.size());
        return Response.success(rsp);
    }


    /**
     * 获取正在抽奖的信息
     *
     * @param actId
     * @param cmptId
     * @return
     */

    @RequestMapping("/getMeetLottery")
    public Response getMeetLottery(@RequestParam("actId") long actId, @RequestParam("cmptInx") long cmptId) {

        CpMeetComponentAttr attr = getComponentAttr(actId, cmptId);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }
        return Response.success(getMeetLottery(attr));
    }


    public CpLotteryRep getMeetLottery(CpMeetComponentAttr attr) {
        Set<String> taskStringList = actRedisDao.zrevRangeByScore(getRedisGroupCode(attr.getActId()), makeKey(attr, MISSION_MAX_FINISH_ZSET), System.currentTimeMillis(), Long.MAX_VALUE);
        if (CollectionUtils.isEmpty(taskStringList)) {
            return null;
        }
        Set<Long> uids = Sets.newHashSet();
        LinkedList<CpTaskFinalInfo> taskFinalList = Lists.newLinkedList();
        for (String taskString : taskStringList) {
            CpTaskFinalInfo info = JsonUtils.deserialize(taskString, CpTaskFinalInfo.class);
            taskFinalList.addFirst(info);
            uids.add(info.getUserUid());
            uids.add(info.getAnchorUid());
        }

        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(Lists.newArrayList(uids));
        Map<String, MemInfo> userInfoMap = batched.getUserInfoMap().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, i -> toMemInfo(i.getValue())));
        List<CpLotteryMember> cpMembers = taskFinalList.stream()
                .map(cpTaskFinalInfo -> {
                    CpLotteryMember lotteryMember = new CpLotteryMember();
                    lotteryMember.setAnchor(userInfoMap.get(String.valueOf(cpTaskFinalInfo.getAnchorUid())));
                    lotteryMember.setUser(userInfoMap.get(String.valueOf(cpTaskFinalInfo.getUserUid())));
                    lotteryMember.setStartTime(cpTaskFinalInfo.getSettleTime());
                    lotteryMember.setEndTime(cpTaskFinalInfo.getSettleTime() + attr.getLotterySec()*1000);
                    lotteryMember.setSid(cpTaskFinalInfo.getSid());
                    lotteryMember.setSsid(cpTaskFinalInfo.getSsid());
                    return lotteryMember;
                })
                .sorted(Comparator.comparingLong(CpLotteryMember::getStartTime).reversed())
                .collect(Collectors.toList());

        NickExt nickExt = JsonUtils.deserialize(batched.getNickExt(), NickExt.class);
        CpLotteryRep rsp = new CpLotteryRep();
        rsp.setCpMembers(cpMembers);
        rsp.setNickExtUsers(nickExt.getUsers());
        rsp.setCurrentTime(System.currentTimeMillis());
        log.info("getMeetAchievement size:{}", cpMembers.size());
        return rsp;
    }


    @HdzjEventHandler(value = UserEnterTemplateEvent.class, canRetry = false)
    public void onUserEnterTemplate(UserEnterTemplateEvent event, CpMeetComponentAttr attr) {
        long uid = event.getUid(), sid = event.getSid(), ssid = event.getSsid();
        String extJson = event.getExtJson();
        log.info("onUserEnterTemplate uid:{},actId:{},extJson:{},sid:{}", uid, attr.getActId(), extJson, sid);
        boolean actEnterEvent = StringUtil.isNotBlank(extJson) && extJson.contains(String.valueOf(attr.getActId()));
        //协议只发1次，确保是本次活动触发的，才弹窗
        if (!actEnterEvent) {
            log.warn("not this actId UserEnterTemplateEvent uid:{}", uid);
            return;
        }
        tipLottery(attr, uid, sid, ssid);
        doPopAwardOnUserEnter(attr, uid, sid, ssid);

    }


    /**
     * 祝福抽奖-公屏发言
     *
     * @param event
     * @param attr
     */
    @HdzjEventHandler(value = ChannelChatTextInnerEvent.class, canRetry = true)
    public void onChannelChatTextInnerEvent(ChannelChatTextInnerEvent event, CpMeetComponentAttr attr) {
        if (attr.getLotteryTaskId() <= 0 || StringUtils.isBlank(attr.getLotteryCommand())) {
            log.info(" onChannelChatTextInnerEvent ignore by attr@event:{}", event);
            return;
        }
        //未包含口令，不处理
        long uid = event.getUid();
        if (!StringUtils.contains(event.getChat(), attr.getLotteryCommand())) {
            log.info(" onChannelChatTextInnerEvent ignore by text@lotteryId:{},uid:{}", event.getChat(), uid);
            return;

        }
        final String groupCode = getRedisGroupCode(attr.getActId());

        long topSid = event.getTopsid(), subSid = event.getSubsid();

        //判断房间是否正在抽奖
        String lotteryInfoKey = makeKey(attr, String.format(LOTTERY_INFO_FORMAT, topSid, subSid));
        Set<ZSetOperations.TypedTuple<String>> lotteryKeys = actRedisDao.zrevRangeByScoreWithScore(groupCode, lotteryInfoKey, System.currentTimeMillis(), Long.MAX_VALUE);
        if (CollectionUtils.isEmpty(lotteryKeys)) {
            return;
        }
        //同个房间可能存在多个抽奖
        String userLotterySeq = "";
        boolean canLottery = false;
        for (ZSetOperations.TypedTuple<String> lotteryInfo : lotteryKeys) {
            //多预留10秒抽奖
            if (lotteryInfo.getScore().longValue() + 10000 < System.currentTimeMillis()) {
                log.info("onChannelChatTextInnerEvent ignore by time@lotteryInfo:{},uid:{}", lotteryInfo, uid);
                continue;
            }

            userLotterySeq = String.format(LOTTERY_SEQ_FORMAT, lotteryInfo.getValue(), uid);
            //判断是否抽过
            if (actRedisDao.setNX(groupCode, makeKey(attr, userLotterySeq), "1")) {
                canLottery = true;
                break;
            }
        }

        if (!canLottery) {
            log.info("onChannelChatTextInnerEvent ignore by user@sid:{},ssid:{},uid:{}", topSid, subSid, uid);
            return;
        }

        //抽奖
        BatchLotteryResult result = hdztAwardServiceClient.doBatchLottery(userLotterySeq, BusiId.SKILL_CARD.getValue(), uid, attr.getLotteryTaskId());
        int code = Optional.ofNullable(result).map(BatchLotteryResult::getCode).orElse(-1);
        Long packageId;
        if (code != 0) {
            log.error("onChannelChatTextInnerEvent lottery error uid:{}, ret:{}", uid, JsonUtil.toJson(result));
            //未抽中
            packageId = attr.getLotteryFailPackageId();
        } else {
            packageId = CollectionUtil.getFirst(result.getRecordIds().keySet());
        }
        Map<Long, AwardModelInfo> longAwardModelInfoMap = packageInfoMap(attr.getLotteryTaskId());
        AwardModelInfo award = longAwardModelInfoMap.get(packageId);

        Map<String, Object> noticeMap = Map.of(
                "icon", award.getPackageImage(),
                "name", award.getPackageName(),
                "hit", !Objects.equals(packageId, attr.getLotteryFailPackageId()));
        //结果弹窗
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), "cpMeetLotteryResult", JsonUtil.toJson(noticeMap), StringUtils.EMPTY, uid);
    }

    /**
     * 进频道抽奖提示
     *
     * @param attr
     * @param uid
     * @param topSid
     * @param subSid
     */

    private void tipLottery(CpMeetComponentAttr attr, long uid, long topSid, long subSid) {
        //判断房间是否在抽奖
        final String groupCode = getRedisGroupCode(attr.getActId());
        String lotteryInfoKey = makeKey(attr, String.format(LOTTERY_INFO_FORMAT, topSid, subSid));
        Set<ZSetOperations.TypedTuple<String>> lotteryKeys = actRedisDao.zrevRangeByScoreWithScore(groupCode, lotteryInfoKey, System.currentTimeMillis(), Long.MAX_VALUE);
        if (CollectionUtils.isEmpty(lotteryKeys)) {
            return;
        }
        boolean needTip = false;
        for (ZSetOperations.TypedTuple<String> lotteryKey : lotteryKeys) {
            String tipKey = makeKey(attr, String.format(LOTTERY_TIP_FORMAT, lotteryKey, uid));
            needTip = actRedisDao.setNX(groupCode, tipKey, "1", 3600);
            if (needTip) {
                break;
            }
        }
        if (!needTip) {
            log.info("tipLottery ignore by user@sid:{},ssid:{},uid:{}", topSid, subSid, uid);
            return;
        }
        String text = " 公屏发送“" + attr.getLotteryCommand() + "”可参与口令抽奖！";
        //抽奖tip
        Map<String, Object> noticeMap = Map.of(
                "text", text,
                "command", attr.getLotteryCommand(),
                "sid", topSid,
                "ssid", subSid
        );
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), "cpMeetLotteryTip", JsonUtil.toJson(noticeMap), StringUtils.EMPTY, uid);
    }


    @HdzjEventHandler(value = TaskProgressChanged.class, canRetry = true)
    public void onTaskProgressChanged(TaskProgressChanged event, CpMeetComponentAttr attr) {
        if (attr.getCpDailyRankId() != event.getRankId() && attr.getCpRankId() != event.getRankId()) {
            return;
        }
        if (attr.getPhaseId() != event.getPhaseId()) {
            return;
        }

        long startIndex = event.getStartTaskIndex(), currIndex = event.getCurrTaskIndex();
        if (startIndex == currIndex) {
            return;
        }


        Date date = DateUtil.getDate(event.getOccurTime(), DateUtil.DEFAULT_PATTERN);
        String channelMember = event.getActors().get(Convert.toLong(attr.getTingRoleId()));
        String[] sidAndSsid = channelMember.split("_");
        String[] members = event.getMember().split("\\|");

        String eKey = StringUtil.isBlank(event.getEkey()) ? event.getSeq() : event.getEkey();


        CpTaskFinalInfo taskFinalInfo = new CpTaskFinalInfo();
        taskFinalInfo.setUserUid(Convert.toLong(members[0]));
        taskFinalInfo.setAnchorUid(Convert.toLong(members[1]));
        taskFinalInfo.setFinishTime(date.getTime());
        taskFinalInfo.setSid(Convert.toLong(sidAndSsid[0]));
        taskFinalInfo.setSsid(Convert.toLong(sidAndSsid[1]));
        //日贡献
        if (attr.getCpDailyRankId() == event.getRankId()) {
            log.info("daily processTaskProgressChanged start seq:{}, event:{}", event.getSeq(), JSON.toJSONString(event));
            eKey = MD5SHAUtil.getMD5(eKey);
            for (long i = startIndex + 1; i <= currIndex; i++) {
                CpTaskFinalInfo taskItem = BeanUtil.copyProperties(taskFinalInfo, CpTaskFinalInfo.class);
                taskItem.setSeq(eKey + i);
                taskItem.setLevel((int) i);
                taskItem.setSettleTime(System.currentTimeMillis());
                taskItem.setTp(DAILY_TP);
                handleDailyTaskComplete(attr, taskItem);
            }
        }
        //总贡献榜
        if (attr.getCpRankId() == event.getRankId()) {
            log.info("processTaskProgressChanged start seq:{}, event:{}", event.getSeq(), JSON.toJSONString(event));
            for (long i = startIndex + 1; i <= currIndex; i++) {
                CpTaskFinalInfo taskItem = BeanUtil.copyProperties(taskFinalInfo, CpTaskFinalInfo.class);
                taskItem.setSeq(eKey + i);
                taskItem.setLevel((int) i);
                taskItem.setSettleTime(System.currentTimeMillis());
                taskItem.setTp(MISSION_TP);
                handleTaskComplete(attr, taskItem);
            }
        }
    }


    private void handleTaskComplete(CpMeetComponentAttr attr, CpTaskFinalInfo taskItem) {
        CpMeetComponentAttr.MissionTaskConfig config = attr.getMissionMap().get(taskItem.getLevel());
        log.info("handleTaskComplete start@{}", taskItem);
        long actId = attr.getActId();
        String groupCode = getRedisGroupCode(actId);
        if (actRedisDao.hsetnx(redisConfigManager.getGroupCode(actId), makeKey(attr, RETRY_SEQ), taskItem.seq, StringUtil.ONE)) {
            //完成记录
            String taskInfoString = JsonUtils.serialize(taskItem);
            actRedisDao.lpush(groupCode, makeKey(attr, MISSION_FINISH_LIST), taskInfoString);
            if (taskItem.getLevel() == 4) {
                //保存最高记录-成就查询
                actRedisDao.zAdd(groupCode, makeKey(attr, MISSION_MAX_FINISH_ZSET), taskInfoString, taskItem.getSettleTime() + attr.getLotterySec()*1000);
                //设置抽奖信息
                String lotteryId = String.format(LOTTERY_KEY_FORMAT, taskItem.anchorUid, taskItem.userUid, System.currentTimeMillis());
                String lotteryInfoKey = makeKey(attr, String.format(LOTTERY_INFO_FORMAT, taskItem.sid, taskItem.ssid));
                actRedisDao.zAdd(groupCode, lotteryInfoKey, lotteryId, taskItem.getSettleTime() + attr.getLotterySec()*1000);
                //全服广播
                broMaxTaskBanner(attr, taskItem);
                //抽奖更新广播 前端展示
                broUpdateMeetLottery(attr);
            }
            //广播给组件
            popLayout(attr, attr.getBusiId(), taskItem);
        }

        //完成弹窗
        popAward(attr, taskItem);
        int busiId = BusiId.findByValue(attr.getBusiId()).getValue();
        BatchWelfareResult batchWelfareUserResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), busiId, taskItem.userUid, attr.getCpMissionTaskId(), 1, config.getUserPackageId(), taskItem.seq + ":" + taskItem.userUid, 3);

        BatchWelfareResult batchWelfareAnchorResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), busiId, taskItem.anchorUid, attr.getCpMissionTaskId(), 1, config.getAnchorPackageId(), taskItem.seq + ":" + taskItem.anchorUid, 3);
        log.info("reward from doWelfare userResult={}, anchorResult={} seq:{}", JsonUtil.toJson(batchWelfareUserResult), JsonUtil.toJson(batchWelfareAnchorResult), taskItem.getSeq());
        doStatic(attr,taskItem);
    }

    /**
     * 完成最高级累计任务全服广播特效
     *
     * @param attr
     * @param taskItem
     */
    private void broMaxTaskBanner(CpMeetComponentAttr attr, CpTaskFinalInfo taskItem) {
        BatchUserInfoWithNickExt batched = webdbThriftClient.batchGetUserInfoWithNickExt(Lists.newArrayList(taskItem.getAnchorUid(), taskItem.getUserUid()));
        Map<Long, MemInfo> userInfoMap = batched.getUserInfoMap().values().stream().map(this::toMemInfo).collect(Collectors.toMap(MemInfo::getUid, Function.identity()));

        MemInfo userMemInfo = userInfoMap.get(taskItem.getUserUid());
        MemInfo anchorMemInfo = userInfoMap.get(taskItem.getAnchorUid());


        //pc广播
        Map<String, String> info = Map.of(
                "svgaUrl", attr.getMaxSvga(),
                "userAvatar", userMemInfo.getAvatar(),
                "userNick", userMemInfo.getName(),
                "anchorAvatar", anchorMemInfo.getAvatar(),
                "anchorNick", anchorMemInfo.getName(),
                "showSec", String.valueOf(attr.getSvgaShowSec()));

        commonBroadCastService.commonBannerBroadcast(0, 0, 0, BroadCastHelpService.changeBusiId2BroTemplate(attr.getBusiId()),
                4, attr.getActId(), 0, 0, CP_BRO_BANNER_ID, 0L, info);

        //app广播
        AppBannerSvgaConfig2 svgaConfig = new AppBannerSvgaConfig2();
        svgaConfig.setDuration(attr.getSvgaShowSec());
        svgaConfig.setSvgaURL(attr.getMaxSvga());

        List<Map<String, AppBannerSvgaText>> broContentLayers = getSvagTextConfig(attr, taskItem.userUid, taskItem.anchorUid);
        svgaConfig.setContentLayers(broContentLayers);

        //头像
        svgaConfig.setImgLayers(buildSvgaImageConfig(attr, List.of(userMemInfo, anchorMemInfo)));

        String seq = taskItem.seq + "_BANNER";
        AppBannerEvent2 appBannerEvent = kafkaService.buildAppBannerEvent2(attr.getActId(), seq,
                BroadCastHelpService.toAppBroBusiness(attr.getBusiId()), FstAppBroadcastType.ALL_TEMPLATE, 0, 0, "", Lists.newArrayList());

        appBannerEvent.setContentType(6);
        appBannerEvent.setAppId(commonService.getTurnoverAppId(Convert.toInt(attr.getBusiId())));
        appBannerEvent.setSvgaConfig(svgaConfig);
        appBannerEvent.setUidList(Lists.newArrayList(taskItem.userUid, taskItem.anchorUid));
        kafkaService.sendAppBannerKafka(appBannerEvent);

        //设置房间特效播放标识
        String bannerShowKey = makeKey(attr, String.format(BANNER_SHOW_INFO_FORMAT, taskItem.sid, taskItem.ssid));
        String groupCode = getRedisGroupCode(attr.getActId());
        actRedisDao.set(groupCode, bannerShowKey, taskItem.seq, attr.getSvgaShowSec());

        log.info("broMaxTaskBanner done,taskItem:{}", taskItem);
    }

    private List<Map<String, String>> buildSvgaImageConfig(CpMeetComponentAttr attr, List<MemInfo> memInfos) {
        List<Map<String, String>> broImgLayers = Lists.newArrayList();
        //广播图片key替换
        String svgaImgKeys = attr.getSvgaImgKeys();
        if (StringUtils.isBlank(svgaImgKeys) || !svgaImgKeys.contains(StrPool.COMMA)) {
            return broImgLayers;
        }
        String[] svgaImgKeyArr = svgaImgKeys.split(StrPool.COMMA);
        MemInfo userInfoVo = memInfos.get(0);
        MemInfo babyInfoVo = memInfos.get(1);
        broImgLayers.add(Map.of(svgaImgKeyArr[0], userInfoVo != null ? userInfoVo.getAvatar() : StringUtil.EMPTY));
        broImgLayers.add(Map.of(svgaImgKeyArr[1], babyInfoVo != null ? babyInfoVo.getAvatar() : StringUtil.EMPTY));

        return broImgLayers;


    }

    private List<Map<String, AppBannerSvgaText>> getSvagTextConfig(CpMeetComponentAttr attr, long userUid, long anchorUid) {
        List<Map<String, AppBannerSvgaText>> broContentLayers = Lists.newArrayList();


        for (BannerSvgaTextConfig bannerSvgaTextConfig : attr.getAppBannerTextList()) {
            String svagText = bannerSvgaTextConfig.getText();
            if (StringUtils.isBlank(svagText)) {
                continue;
            }
            //替换uid
            svagText = svagText.replace("{userNick}", "{" + userUid + ":n}");
            svagText = svagText.replace("{anchorNick}", "{" + anchorUid + ":n}");
            AppBannerSvgaText appBannerSvgaText = new AppBannerSvgaText();
            appBannerSvgaText.setText(svagText);

            if (StringUtil.isNotBlank(attr.getSvgaFontSize())) {
                appBannerSvgaText.setFontSize(JSON.parseObject(attr.getSvgaFontSize(), Map.class));
            }
            broContentLayers.add(Map.of(bannerSvgaTextConfig.getKey(), appBannerSvgaText));

        }

        return broContentLayers;

    }

    private void broUpdateMeetLottery(CpMeetComponentAttr attr) {
        CpLotteryRep meetLottery = getMeetLottery(attr);
        commonBroadCastService.commonBannerBroadcast(0, 0, 0, BroadCastHelpService.changeBusiId2BroTemplate(attr.getBusiId()),
                4, attr.getActId(), 0, 0, CP_BRO_LOTTERY_BANNER_ID, 0L, JsonUtils.serialize(meetLottery));
    }

    /**
     * 完成日任务给主播和用户发奖励
     *
     * @param attr
     * @param taskItem
     */
    private void handleDailyTaskComplete(CpMeetComponentAttr attr, CpTaskFinalInfo taskItem) {
        long awardTaskId = attr.getCpMissionTaskId();
        CpMeetComponentAttr.MissionTaskConfig taskConfig = attr.getDailyMissionMap().get(taskItem.level);
        if (actRedisDao.hsetnx(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr, RETRY_DAILY_SEQ), taskItem.seq, StringUtil.ONE)) {
            popAward(attr, taskItem);
        }

        BatchWelfareResult batchWelfareUserResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(), taskItem.userUid, awardTaskId, 1, taskConfig.getUserPackageId(), taskItem.seq + ":" + taskItem.userUid, 3);

        BatchWelfareResult batchWelfareAnchorResult = hdztAwardServiceClient.doWelfare(DateUtil.getNowYyyyMMddHHmmss(), BusiId.GAME_ECOLOGY.getValue(), taskItem.anchorUid, awardTaskId, 1, taskConfig.getAnchorPackageId(), taskItem.seq + ":" + taskItem.anchorUid, 3);
        log.info("daily reward from doWelfare userResult={}, anchorResult={} seq:{}", JsonUtil.toJson(batchWelfareUserResult), JsonUtil.toJson(batchWelfareAnchorResult), taskItem.seq);
        doStatic(attr,taskItem);
    }


    /**
     * 榜单改变事件,首次触达广播
     **/
    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankScoreChange(RankingScoreChanged event, CpMeetComponentAttr attr) {
        log.info("rankScoreChange event:{}", JsonUtil.toJson(event));
        if (event.getRankId() != attr.getCpDailyRankId()) {
            return;
        }
        long total = event.getRankScore();
        if (total - event.getItemScore() >= attr.getScoreThreshold() || total < attr.getScoreThreshold()) {
            return;
        }

        //第一等级的每日任务
        CpMeetComponentAttr.MissionTaskConfig missionTaskConfig = attr.getDailyMissionMap().get(1);
        if (missionTaskConfig == null) {
            return;
        }
        //达成目标了，不再提示
        long diff = missionTaskConfig.getGoalScore() - total;
        if (diff <= 0) {
            return;
        }

        long uid = Convert.toLong(event.getMember().split("\\|")[1]);
        if (!actRedisDao.hsetnx(redisConfigManager.getGroupCode(attr.getActId()), makeKey(attr, SCORE_CHANGE_POP), uid + "", commonService.getNow(attr.getActId()).getTime() + "")) {
            log.info("rankScoreChange uid:{} already pop", uid);
            return;
        }


        Map<Long, AwardModelInfo> packageInfo = packageInfoMap(attr.getCpMissionTaskId());
        AwardModelInfo awardModelInfo = packageInfo.get(missionTaskConfig.getAnchorPackageId());
        String giftName = awardModelInfo.getPackageName();
        String giftIcon = awardModelInfo.getPackageImage();

        popScoreChange(attr, diff, giftName, giftIcon, uid);

    }


    public Map<Long, AwardModelInfo> packageInfoMap(long taskId) {
        try {
            var result = hdztAwardServiceClient.queryAwardTasks(taskId);
            return result == null ? Collections.emptyMap() : result;
        } catch (Exception e) {
            log.error("hdztAwardServiceClient.queryAwardTasks", e);
        }
        return Collections.emptyMap();
    }

    private void popScoreChange(CpMeetComponentAttr attr, long diffScore, String giftName, String giftIcon, long uid) {

        Map<String, Object> noticeMap = Map.of("diffScore", diffScore, "giftName", giftName, "giftIcon", giftIcon);
        commonBroadCastService.commonNoticeUnicast(attr.getActId(), "cpMeetTaskFirstTip", JsonUtils.serialize(noticeMap), Strings.EMPTY, uid);

        String appText = String.format("<font color='#ffffff'>今日热恋值还差%d达LV1等级，将可获得%s</font>{img}", diffScore, giftName);
        List<String> imageList = List.of(giftIcon);

        commonBroadCastService.appUnicastCommonTopBanner(attr.getActId(), uid, attr.getBusiId(), attr.getScoreThresholdBgUrl(), attr.getScoreThresholdIconUrl(), appText, imageList, null);
    }


    private void popAward(CpMeetComponentAttr attr, CpTaskFinalInfo taskItem) {
        Map<Long, AwardModelInfo> awardModelInfoMap = packageInfoMap(attr.getCpMissionTaskId());
        CpMeetComponentAttr.MissionTaskConfig taskConfig = taskItem.tp == DAILY_TP ? attr.getDailyMissionMap().get(taskItem.level) : attr.getMissionMap().get(taskItem.level);

        String cpMember = taskItem.userUid + "|" + taskItem.anchorUid;

        //主播
        AwardModelInfo awardModelInfo = awardModelInfoMap.get(taskConfig.getAnchorPackageId());
        JSONObject extJson = new JSONObject();
        extJson.put("icon", awardModelInfo.getPackageImage() == null ? "" : awardModelInfo.getPackageImage());
        extJson.put("gift", awardModelInfo.getPackageName() == null ? "" : awardModelInfo.getPackageName());
        extJson.put("tp", taskItem.getTp());
        extJson.put("level", taskItem.getLevel());
        extJson.put("levelName", taskConfig.getTaskName());
        extJson.put("cpMemberKey", cpMember+"|2");

        PopAwardInfo anchorAwardInfo = new PopAwardInfo();
        anchorAwardInfo.setActId(attr.getActId());
        anchorAwardInfo.setContest(extJson.toJSONString());
        anchorAwardInfo.setUid(taskItem.anchorUid);
        anchorAwardInfo.setSid(taskItem.sid);
        anchorAwardInfo.setSsid(taskItem.ssid);
        anchorAwardInfo.setCmptUseInx(attr.getCmptUseInx());
        popAward(attr, anchorAwardInfo, true);


        //用户
        awardModelInfo = awardModelInfoMap.get(taskConfig.getUserPackageId());
        extJson.put("icon", awardModelInfo.getPackageImage() == null ? "" : awardModelInfo.getPackageImage());
        extJson.put("gift", awardModelInfo.getPackageName() == null ? "" : awardModelInfo.getPackageName());
        extJson.put("cpMemberKey", cpMember + "|1");

        PopAwardInfo userAwardInfo = new PopAwardInfo();
        userAwardInfo.setActId(attr.getActId());
        userAwardInfo.setContest(extJson.toJSONString());
        userAwardInfo.setUid(taskItem.userUid);
        userAwardInfo.setSid(taskItem.sid);
        userAwardInfo.setSsid(taskItem.ssid);
        userAwardInfo.setCmptUseInx(attr.getCmptUseInx());
        popAward(attr, userAwardInfo, true);

    }

    public void doPopAwardOnUserEnter(CpMeetComponentAttr attr, long uid, long sid, long ssid) {
        String groupCode = redisConfigManager.getGroupCode(attr.getActId());
        String userPopKey = makeKey(attr, String.format(USER_AWARD_POP_KEY_FORMAT, uid));
        Set<String> popStringList = actRedisDao.sMembers(groupCode, userPopKey);
        if (CollectionUtils.isEmpty(popStringList)) {
            return;
        }
        for (String popString : popStringList) {
            PopAwardInfo info = JsonUtils.deserialize(popString, PopAwardInfo.class);
            popAward(attr, info, false);
            actRedisDao.sRem(groupCode, userPopKey, popString);
            log.info("doPopAwardOnUserEnter  one done@{}", popString);
        }
    }

    public void doDelayPopAward(CpMeetComponentAttr componentAttr, Object event) {
        if (!(event instanceof PopAwardInfo info)) {
            return;
        }

        if (componentAttr == null) {
            return;
        }
        popAward(componentAttr, info, true);
        log.info("doDelayPopAward done@{}", info);
    }

    private void popAward(CpMeetComponentAttr attr, PopAwardInfo popAwardInfo, boolean checkOnChannel) {
        String groupCode = redisConfigManager.getGroupCode(popAwardInfo.getActId());
        String bannerShowKey = makeKey(attr, String.format(BANNER_SHOW_INFO_FORMAT, popAwardInfo.sid, popAwardInfo.ssid));
        Long expire = actRedisDao.getRedisTemplate(groupCode).boundGeoOps(bannerShowKey).getExpire();
        //房间有特效
        if (expire != null && expire > 0) {
            final String delayKey1 = Const.addActivityPrefix(attr.getActId(), DELAY_AWARD_POP);
            long expiredMills = System.currentTimeMillis() + expire * 1000;
            publishDelayEvent(attr, DELAY_AWARD_POP, popAwardInfo, expiredMills);
            return;
        }


        if (checkOnChannel) {
            UserCurrentChannel channel = commonService.getUserCurrentChannel(popAwardInfo.getUid());
            // 不在频道内，或不在member对应的频道内，先保存起来
            if (channel == null || channel.getTopsid() != popAwardInfo.getSid() || channel.getSubsid() != popAwardInfo.getSsid()) {
                String userPopKey = makeKey(attr, String.format(USER_AWARD_POP_KEY_FORMAT, popAwardInfo.uid));
                actRedisDao.sadd(groupCode, userPopKey, JsonUtils.serialize(popAwardInfo));
                log.info("save popAwardInfo  with popAwardInfo:{} ", popAwardInfo);
                return;
            }
        }
        commonBroadCastService.commonNoticeUnicast(popAwardInfo.getActId(), "cpMeetTaskAward",
                popAwardInfo.getContest(), Strings.EMPTY, popAwardInfo.getUid());
    }

    private void popLayout(CpMeetComponentAttr attr, int busiId, CpTaskFinalInfo taskItem) {
        List<Long> uids = Lists.newArrayList(taskItem.getUserUid(), taskItem.getAnchorUid());
        Map<Long, UserBaseInfo> userBaseInfoMap = commonService.batchGetUserInfos(uids, false);
        UserBaseInfo anchorUserBaseInfo = userBaseInfoMap.get(taskItem.anchorUid);
        UserBaseInfo userBaseInfo = userBaseInfoMap.get(taskItem.userUid);
        CpMeetComponentAttr.MissionTaskConfig missionTaskConfig = attr.getMissionMap().get(taskItem.level);
        Map<String, Object> ext = Maps.newHashMap();
        ext.put("anchorAvatar", anchorUserBaseInfo == null ? "https://s1.yy.com/guild/header/10001.jpg" : anchorUserBaseInfo.getHdLogo());
        ext.put("userAvatar", userBaseInfo == null ? "https://s1.yy.com/guild/header/10001.jpg" : userBaseInfo.getHdLogo());
        ext.put("sid", taskItem.sid);
        ext.put("ssid", taskItem.ssid);
        ext.put("level", taskItem.level);
        ext.put("levelText", missionTaskConfig.getTaskText());
        //等级 分频道广播
        int broType = taskItem.level > 2 ? 4 : 3;
        commonBroadCastService.commonBannerBroadcast(taskItem.sid, taskItem.ssid, 0, BroadCastHelpService.changeBusiId2BroTemplate(busiId),
                broType, attr.getActId(), taskItem.anchorUid, 0, CP_BRO_LAYOUT_BANNER_ID, 0L, ext);
    }


    private MemInfo toMemInfo(WebdbUserInfo userBaseInfo) {
        MemInfo memInfo = new MemInfo();
        memInfo.setUid(Long.parseLong(userBaseInfo.getUid()));
        memInfo.setName(userBaseInfo.getNick());
        memInfo.setAvatar(WebdbUtils.getLogo(userBaseInfo));
        return memInfo;
    }


    public void doStaticReport(long actId, CpMeetComponentAttr attr, Date noticeDate) {
        if(attr==null) {
            log.error("doStaticReport doStaticReport attr is null,actId:{}",actId);
            return;
        }
        String groupCode = getRedisGroupCode(attr.getActId());
        String staticTime = DateUtil.format(noticeDate, DateUtil.PATTERN_TYPE2);
        List<String> dailyKeys = List.of(
                makeKey(attr, String.format(STATIC_INFO_FORMAT, staticTime, DAILY_TP, 1)),
                makeKey(attr, String.format(STATIC_INFO_FORMAT, staticTime, DAILY_TP, 2)),
                makeKey(attr, String.format(STATIC_INFO_FORMAT, staticTime, DAILY_TP, 3)),
                makeKey(attr, String.format(STATIC_INFO_FORMAT, staticTime, DAILY_TP, 4)));

        List<String> dailyValues = actRedisDao.getRedisTemplate(groupCode).opsForValue().multiGet(dailyKeys);

        StringBuilder dailyContent = new StringBuilder();
        for (int i = 0; i < dailyKeys.size(); i++) {
            String value = dailyValues.get(i);
            dailyContent.append("每日任务LV").append(i + 1).append("达成次数:").append(Convert.toInt(value)).append("\n");
        }
        String dailyMsg = buildActRuliuMsg(actId, false, "七夕相会每日任务", dailyContent.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, dailyMsg, Lists.newArrayList());



        List<String> taskKeys = List.of(
                makeKey(attr, String.format(STATIC_INFO_FORMAT, StringUtils.EMPTY, MISSION_TP, 1)),
                makeKey(attr, String.format(STATIC_INFO_FORMAT, StringUtils.EMPTY, MISSION_TP, 2)),
                makeKey(attr, String.format(STATIC_INFO_FORMAT, StringUtils.EMPTY, MISSION_TP, 3)),
                makeKey(attr, String.format(STATIC_INFO_FORMAT, StringUtils.EMPTY, MISSION_TP, 4)));

        //奖品价值
        Map<Integer, Integer> awardValueMap = Map.of(1, 28, 2, 99, 3, 198, 4, 495);
        List<String> taskValues = actRedisDao.getRedisTemplate(groupCode).opsForValue().multiGet(taskKeys);
        StringBuilder content = new StringBuilder();
        long awardValue = 0;
        for (int i = 0; i < taskValues.size(); i++) {
            Integer value = Convert.toInt(taskValues.get(i));
            content.append("累计任务LV").append(i + 1).append("达成次数:").append(value).append("\n");
            awardValue += (long) value * MapUtils.getInteger(awardValueMap, i + 1, 0) * 2;
        }
        content.append("累计发放总礼物金额:").append(awardValue);
        String msg = buildActRuliuMsg(actId, false, "七夕相会累计任务", content.toString());
        baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());

    }

    public void doStatic(CpMeetComponentAttr attr, CpTaskFinalInfo taskItem) {
        try {

            Date date = new Date(taskItem.getFinishTime());
            String staticTime = taskItem.getTp() == MISSION_TP ? "" : DateUtil.format(date, DateUtil.PATTERN_TYPE2);
            String key = makeKey(attr, String.format(STATIC_INFO_FORMAT, staticTime, taskItem.getTp(), taskItem.level));
            String groupCode = getRedisGroupCode(attr.getActId());
            actRedisDao.incrValue(groupCode, key, 1);

            log.info("doStatic info@time:{},taskItem:{}", staticTime, taskItem);
            if (taskItem.getTp() == MISSION_TP && taskItem.getLevel() == 4) {
                Map<Long, UserInfoVo> userInfoVoMap = userInfoService.getUserInfo(Lists.newArrayList(taskItem.getAnchorUid(), taskItem.getUserUid()), Template.getTemplate(810));
                String anchorNick = userInfoVoMap.containsKey(taskItem.getAnchorUid()) ? userInfoVoMap.get(taskItem.getAnchorUid()).getNick() : "";
                String userNick = userInfoVoMap.containsKey(taskItem.getUserUid()) ? userInfoVoMap.get(taskItem.getUserUid()).getNick() : "";
                String nickString = String.format("%s & %s", userNick, anchorNick);
                String uidString = String.format("%d & %d", taskItem.getUserUid(), taskItem.getAnchorUid());
                String fly = String.format("yy://pd-[sid=%d&subid=%d]", taskItem.getSid(), taskItem.getSsid());
                String content = "CP昵称：" + nickString + "\n" +
                        "UID：" + uidString + "\n" +
                        "飞机票：" + fly + "\n" +
                        "达成时间：" + DateUtil.format(date) + "\n";

                String msg = buildActRuliuMsg(attr.getActId(), false, "七夕相会玩法-实时达成", content);
                baiduInfoFlowRobotService.asyncSendNotifyConfigKey(GeParamName.IMGroup.IMG_IT_ACTIVITY_INFO_REPORT_TWO, msg, Lists.newArrayList());
            }

        } catch (Exception e) {
            log.error("doStatic error@data:{} {}", taskItem, e.getMessage(), e);
        }
    }

    @Data
    public static class CpTaskFinalInfo {

        private String seq;

        private long userUid;

        private long anchorUid;

        private int level;

        //真是时间，用于抽奖
        private long settleTime;

        //模拟时间，用于展示
        private long finishTime;

        private long sid;

        private long ssid;

        private int tp;

    }


    @Data
    public static class CpAchievementMember {
        private String cpMember;
        private MemInfo anchor;
        private MemInfo user;
        private long finishTime;
    }

    @Data
    public static class CpAchievementRep {
        private List<CpAchievementMember> cpMembers;
        private Map<String, Map<String, MultiNickItem>> nickExtUsers;
    }

    @Data
    public static class CpLotteryMember {
        private MemInfo anchor;
        private MemInfo user;
        private long sid;
        private long ssid;
        private long startTime;
        private long endTime;
    }

    @Data
    public static class CpLotteryInfo {
        private String id;
        private long startTime;
        private long endTime;
    }

    @Data
    public static class CpLotteryRep {
        private long currentTime;
        private List<CpLotteryMember> cpMembers;
        private Map<String, Map<String, MultiNickItem>> nickExtUsers;
    }



    @Data
    private static class PopAwardInfo {

        private long actId;
        private long cmptUseInx;
        private long uid;
        private String contest;
        private long sid;
        private long ssid;
    }


    @Data
    private static class CpMember {
        private String cpMember;

        private MemInfo anchor;

        private MemInfo user;

        private long score;
    }

    @Data
    private static class MemInfo {
        private long uid;

        private String name;

        private String avatar;
    }

    @Data
    public static class ScrollCpInfoRep {
        private List<ScrollCpInfo> list;
        private Map<String, Map<String, MultiNickItem>> nickExtUsers;
    }

    @Data
    private static class ScrollCpInfo {

        private MemInfo anchor;

        private MemInfo user;

        private int level;

        private String levelName;

        private long sid;

        private long ssid;
    }


    @Data
    private static class CpMission {
        private List<Mission> missions;

        private List<Mission> dailyMissions;

        private long dailyScore;

        private long totalScore;

        private String avatar;

        private String nick;
    }

    @Data
    private static class Mission {
        private long taskId;

        private boolean finish;

        private long score;
    }

    @Data
    private static class CpListRsp {
        List<CpMember> cpMembers;

        Map<String, Map<String, MultiNickItem>> nickExtUsers;
    }

    @Override
    public Long getComponentId() {
        return ComponentId.CP_MEET;
    }
}
