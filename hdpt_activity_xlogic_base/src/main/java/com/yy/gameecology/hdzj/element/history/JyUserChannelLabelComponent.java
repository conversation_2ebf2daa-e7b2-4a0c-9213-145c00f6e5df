package com.yy.gameecology.hdzj.element.history;

import com.google.common.collect.ImmutableSortedMap;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.RankingScoreChanged;
import com.yy.gameecology.activity.bean.rank.RankValueItemBase;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.processor.ranking.ComponentRankingExtHandle;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.history.attr.JyUserChannelLabelComponentAttr;
import com.yy.thrift.hdztranking.Rank;
import com.yy.thrift.hdztranking.RankingInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;


/**
 * @Author: CXZ
 * @Desciption: 交友用户公会数量标签组件
 * @Date: 2021/4/15 16:46
 * @Modified:
 */
@Deprecated
@Component
public class JyUserChannelLabelComponent extends BaseActComponent<JyUserChannelLabelComponentAttr> implements ComponentRankingExtHandle<JyUserChannelLabelComponentAttr> {

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    private final static String USER_CHANNEL_COUNT_KEY = "user_channel_count";
    private final static String USER_CHANNEL_SET_KEY = "user_channel_set";

    private final static long CHANNEL_ROLE_ID = 50004L;

    @Override
    public Long getComponentId() {
        return ComponentId.JY_USER_CHANNEL_COUNT_LABEL;
    }

    @HdzjEventHandler(value = RankingScoreChanged.class, canRetry = true)
    public void onRankingScoreChanged(RankingScoreChanged event, JyUserChannelLabelComponentAttr attr) {

        if (event.getRankId() != attr.getRankId() || event.getPhaseId() != attr.getPhaseId()) {
            return;
        }
        //找不到公会或者分数是0
        String channelId = event.getActors().get(CHANNEL_ROLE_ID);
        if (StringUtils.isBlank(channelId) || event.getItemScore() <= 0) {
            return;
        }

        String userUid = event.getMember().trim();
        String userChannel = userUid + "_" + channelId.trim();
        String groupCode = redisConfigManager.getGroupCode(event.getActId());
        if (actRedisDao.sAdd(groupCode, makeKey(attr, USER_CHANNEL_SET_KEY), userChannel) == 1) {
            long userChannelCount = actRedisDao.zIncrWithSeq(groupCode, event.getSeq(), makeKey(attr, USER_CHANNEL_COUNT_KEY), userUid + "", 1);
            log.info("onRankingScoreChanged done@actId:{},useId:{},sid:{},user channel count:{}", attr.getActId(), userUid, channelId, userChannelCount);
        }
        log.info("onRankingScoreChanged done -> event:{}, attr:{}", event, attr);
    }

    public Integer getUserLabel(JyUserChannelLabelComponentAttr componentAttr, long uid) {
        String groupCode = redisConfigManager.getGroupCode(componentAttr.getActId());
        int count = (int) actRedisDao.zscore(groupCode, makeKey(componentAttr, USER_CHANNEL_COUNT_KEY), uid + "");
        Map.Entry<Integer, Integer> entry = ImmutableSortedMap.copyOf(componentAttr.getLabelMap()).floorEntry(count);
        return entry != null ? entry.getValue() : 0;
    }

    /**
     * 为榜单增加用户公会标签
     *
     * @param attr
     * @param rankReq     请求数据
     * @param rankingInfo 榜单信息
     * @param ranks       榜单原始数据
     * @param objectList  榜单展示数据，已经填充了成员信息
     * @return
     */
    @Override
    public List<Object> handleExt(JyUserChannelLabelComponentAttr attr, GetRankReq rankReq, RankingInfo rankingInfo, List<Rank> ranks, List<Object> objectList) {

        final boolean match = rankingInfo.getExtData() == null || !rankingInfo.getExtData().containsKey("tb_ranking_config:jyUserChannelLabel");
        if (match) {
            return objectList;
        }
        String config = rankingInfo.getExtData().get("tb_ranking_config:jyUserChannelLabel");
        Long userMinScore = Long.parseLong(config);

        objectList.stream().map(rank -> ((RankValueItemBase) rank))
                .forEach(rank -> {
                            int label = 0;
                            if (rank.getValue() >= userMinScore) {
                                label = getUserLabel(attr, Long.valueOf(rank.getKey()));
                            }
                            if (rank.getViewExt() == null) {
                                rank.setViewExt(Maps.newHashMap());
                            }
                            rank.getViewExt().put("userLabel", label + "");
                        }
                );
        return objectList;
    }
}
