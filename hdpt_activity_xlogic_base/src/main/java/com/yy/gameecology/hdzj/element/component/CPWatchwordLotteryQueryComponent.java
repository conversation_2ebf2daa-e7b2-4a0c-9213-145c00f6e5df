package com.yy.gameecology.hdzj.element.component;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2062LotteryBox;
import com.yy.gameecology.common.utils.RankUtils;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CPWatchwordLotteryQueryComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.ChannelWatchwordLotteryComponentAttr;
import com.yy.gameecology.hdzj.element.component.dao.ChannelWatchwordLotteryDao;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("5152")
public class CPWatchwordLotteryQueryComponent extends BaseActComponent<CPWatchwordLotteryQueryComponentAttr> {

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private ChannelWatchwordLotteryDao channelWatchwordLotteryDao;

    @Autowired
    private ChannelWatchwordLotteryComponent channelWatchwordLotteryComponent;

    @Override
    public Long getComponentId() {
        return ComponentId.WATCHWORD_LOTTERY_QUERY;
    }

    @GetMapping("boxes")
    public Response<List<CPWatchwordLotteryInfo>> queryLotteryBoxes(@RequestParam(name = "actId") long actId,
                                                                    @RequestParam(name = "cmptInx") long cmptInx) {
        var attr = getComponentAttr(actId, cmptInx);
        if (attr == null) {
            return Response.fail(400, "component not exist");
        }
        Date now = commonService.getNow(actId);
        List<Cmpt2062LotteryBox> boxes = new ArrayList<>(attr.getCountLimit() + attr.getAdditionCount());
        boxes.addAll(channelWatchwordLotteryDao.selectLotteryBoxes(actId, attr.getWatchwordLotteryIndexes(), now, attr.getCountLimit()));
        if (CollectionUtils.isNotEmpty(attr.getAdditionIndexes())) {
            boxes.addAll(channelWatchwordLotteryDao.selectLotteryBoxes(actId, attr.getAdditionIndexes(), now, attr.getAdditionCount()));
        }

        if (CollectionUtils.isEmpty(boxes)) {
            return Response.success(Collections.emptyList());
        }

        List<CPWatchwordLotteryInfo> result = new ArrayList<>(boxes.size());
        Set<Long> uids = new HashSet<>(boxes.size() * 2);

        Map<Long, ChannelWatchwordLotteryComponentAttr> componentMap = boxes.stream()
                .map(Cmpt2062LotteryBox::getCmptUseInx)
                .distinct()
                .collect(Collectors.toMap(Function.identity(), index -> channelWatchwordLotteryComponent.getComponentAttr(actId, index)));

        for (var box : boxes) {
            var cpUids = RankUtils.getCpUidByMemberId(box.getMemberId());
            if (cpUids == null) {
                continue;
            }

            final long userUid = cpUids.getLeft(), anchorUid = cpUids.getRight();
            uids.add(userUid);
            uids.add(anchorUid);
            CPWatchwordLotteryInfo item = new CPWatchwordLotteryInfo();
            item.setId(box.getSeq());
            item.setBoxType(box.getCmptUseInx());
            item.setUserUid(userUid);
            item.setBabyUid(anchorUid);
            item.setSid(box.getSid());
            item.setSsid(box.getSsid());
            item.setCountdown(DateUtil.between(now, box.getExpiredTime(), DateUnit.SECOND));

            var boxAttr = componentMap.get(box.getCmptUseInx());
            if (boxAttr != null) {
                item.setWatchword(boxAttr.getChatText());
            }

            result.add(item);
        }

        Map<String, Map<String, MultiNickItem>> multiNickUsers = new HashMap<>(uids.size());
        Map<Long, UserInfoVo> userInfoMap = userInfoService.getUserInfoWithNickExt(List.copyOf(uids), multiNickUsers);

        for (var item : result) {
            var userInfo = userInfoMap.get(item.getUserUid());
            if (userInfo != null) {
                item.setUserLogo(userInfo.getAvatarUrl());
                item.setUserNick(userInfo.getNick());
                item.setWatchword(StringUtils.replace(item.getWatchword(), "{userNick}", userInfo.getNick()));
            }

            userInfo = userInfoMap.get(item.getBabyUid());
            if (userInfo != null) {
                item.setBabyLogo(userInfo.getAvatarUrl());
                item.setBabyNick(userInfo.getNick());
                item.setWatchword(StringUtils.replace(item.getWatchword(), "{babyNick}", userInfo.getNick()));
            }
        }

        return Response.success(result, multiNickUsers);
    }

    @Getter
    @Setter
    public static class CPWatchwordLotteryInfo {
        protected String id;

        protected long boxType;

        protected long userUid;

        protected long babyUid;

        protected String userLogo;

        protected String userNick;

        protected String babyLogo;

        protected String babyNick;

        protected String watchword;

        protected long sid;

        protected long ssid;

        protected long countdown;
    }
}
