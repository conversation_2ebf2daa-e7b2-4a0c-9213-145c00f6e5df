package com.yy.gameecology.hdzj.delay;

import cn.hutool.core.lang.Assert;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTask;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;

@Slf4j
@Component
public class ComponentDelayQueue implements SchedulingConfigurer, ApplicationListener<ContextRefreshedEvent> {

    private final RedisConfigManager redisConfigManager;

    private final ActRedisGroupDao redisGroupDao;

    private final ThreadPoolTaskScheduler threadPoolTaskScheduler;

    private ScheduledTaskRegistrar taskRegistrar;

    private volatile boolean closed = false;

    /**
     * 定时器 用于动态生成（或销毁）配置了组件的scheduled task
     */
    private static final ThreadPoolTaskScheduler TASK_SCHEDULER = new ThreadPoolTaskScheduler();

    static {
        TASK_SCHEDULER.setThreadNamePrefix("delay-queue-daemon-");
        TASK_SCHEDULER.setWaitForTasksToCompleteOnShutdown(true);
        TASK_SCHEDULER.setAwaitTerminationSeconds(2);
        TASK_SCHEDULER.initialize();
    }

    public ComponentDelayQueue(RedisConfigManager redisConfigManager, ActRedisGroupDao redisGroupDao, ThreadPoolTaskScheduler threadPoolTaskScheduler) {
        this.redisConfigManager = redisConfigManager;
        this.redisGroupDao = redisGroupDao;
        this.threadPoolTaskScheduler = threadPoolTaskScheduler;
    }

    /**
     * 记录被注册了哪些组件延迟队列
     */
    private static final ConcurrentHashMap<DelayKey, DelayComponent<?>> DELAY_COMPONENTS = new ConcurrentHashMap<>();

    public <T extends ComponentAttr> void register(BaseActComponent<T> component, String key, BiConsumer<T, Object> consumer) {
        DelayKey delayKey = new DelayKey(component.getComponentId(), key);
        DELAY_COMPONENTS.computeIfAbsent(delayKey, k -> {
            DelayComponent<T> delayComponent = new DelayComponent<>();
            delayComponent.setComponent(component);
            delayComponent.setConsumer(consumer);
//            refreshDelayComponent(delayKey, delayComponent);
            log.info("registered delayKey:{}", delayKey);
            return delayComponent;
        });
    }

    public void publishDelayEvent(Long actId, Long componentId, Long componentIndex, String key, Object event, long expiredMills) {
        Assert.notNull(event, "delay event cannot be null");
        DelayKey delayKey = new DelayKey(componentId, key);
        if (!DELAY_COMPONENTS.containsKey(delayKey)) {
            throw new IllegalStateException("component delay consume with " + delayKey + " not registered");
        }
        String redisKey = BaseActComponent.makeKey(actId, componentId, componentIndex, key);
        final byte[] data = RedisSerializer.json().serialize(event);
        if (data != null) {
            getRedisTemplate(actId).execute((RedisCallback<Object>) connection -> connection.zAdd(redisKey.getBytes(StandardCharsets.UTF_8), expiredMills, data));
            log.info("publishDelayEvent: {} {} {}", redisKey, event, expiredMills);
        }
    }

    private <T extends ComponentAttr> void refreshDelayComponent(final DelayKey delayKey, final DelayComponent<T> delayComponent) {
        if (SysEvHelper.isLocal() || SysEvHelper.isHistory()) {
            log.warn("trying to refresh delay:{} but env is local or history", delayKey);
            return;
        }

        synchronized (delayComponent) {
            if (closed) {
                return;
            }

            log.info("going to refresh delay:{}", delayKey);
            final Map<Long, Map<Long, ScheduledTask>> tasks = delayComponent.scheduledTasks;
            BaseActComponent<T> component = delayComponent.getComponent();
            Set<Long> actIds = component.getComponentEffectActIds();
            if (CollectionUtils.isEmpty(actIds)) {
                if (MapUtils.isEmpty(tasks)) {
                    return;
                }

                Set<Long> existActIds = new HashSet<>(tasks.keySet());
                cancelTaskByActIds(delayKey, tasks, existActIds);
                return;
            }

            for (long actId : actIds) {
                Map<Long, ScheduledTask> actTasks = tasks.get(actId);
                List<T> attrs = component.getAllComponentAttrs(actId);
                if (CollectionUtils.isEmpty(attrs) && MapUtils.isNotEmpty(actTasks)) {
                    cancelTaskByActId(delayKey, tasks, actId);
                    continue;
                }

                if (actTasks == null) {
                    actTasks = new HashMap<>(attrs.size());
                    tasks.put(actId, actTasks);
                }

                Set<Long> removing = new HashSet<>(actTasks.keySet());
                for (ComponentAttr attr : attrs) {
                    long componentIndex = attr.getCmptUseInx();
                    removing.remove(componentIndex);
                    if (!actTasks.containsKey(componentIndex)) {
                        DelayTask<T> task = new DelayTask<>(component, delayKey.key, actId, componentIndex, delayComponent.consumer);
                        ScheduledTask scheduledTask = taskRegistrar.scheduleFixedDelayTask(task);
                        if (scheduledTask != null) {
                            actTasks.put(componentIndex, scheduledTask);
                            log.info("added delay consume task: {} {} {}", delayKey, actId, componentIndex);
                        }
                    }
                }

                if (!removing.isEmpty()) {
                    cancelTaskByActIdComponentIndex(delayKey, tasks, actId, removing);
                }
            }
        }
    }

    private <T extends ComponentAttr> void shutdownConsume(final DelayComponent<T> delayComponent) {
        synchronized (delayComponent) {
            final Map<Long, Map<Long, ScheduledTask>> tasks = delayComponent.scheduledTasks;
            if (MapUtils.isNotEmpty(tasks)) {
                tasks.values().stream()
                        .filter(MapUtils::isNotEmpty)
                        .flatMap(map -> map.values().stream())
                        .filter(Objects::nonNull)
                        .forEach(ScheduledTask::cancel);
            }
        }
    }

    private void cancelTaskByActIds(DelayKey delayKey, Map<Long, Map<Long, ScheduledTask>> tasks, Set<Long> actIds) {
        if (closed) {
            return;
        }
        actIds.forEach(actId -> {
            Map<Long, ScheduledTask> componentTask = tasks.get(actId);
            if (MapUtils.isNotEmpty(componentTask)) {
                componentTask.values().forEach(ScheduledTask::cancel);
            }

            tasks.remove(actId);
            log.info("removed scheduled task: {} {}", delayKey, actId);
        });
    }

    private void cancelTaskByActId(DelayKey delayKey, Map<Long, Map<Long, ScheduledTask>> tasks, Long actId) {
        if (closed) {
            return;
        }
        Map<Long, ScheduledTask> componentTask = tasks.get(actId);
        if (MapUtils.isNotEmpty(componentTask)) {
            componentTask.values().forEach(ScheduledTask::cancel);
        }

        tasks.remove(actId);
        log.info("removed scheduled task: {} {}", delayKey, actId);
    }

    private void cancelTaskByActIdComponentIndex(DelayKey delayKey, Map<Long, Map<Long, ScheduledTask>> tasks, Long actId, Set<Long> componentIndex) {
        if (closed) {
            return;
        }
        Map<Long, ScheduledTask> componentTask = tasks.get(actId);
        if (MapUtils.isNotEmpty(componentTask)) {
            componentIndex.forEach(index -> {
                componentTask.get(index).cancel();
                componentTask.remove(index);
                log.info("removed scheduled task: {} {} {}", delayKey, actId, index);
            });

            if (componentTask.isEmpty()) {
                tasks.remove(actId);
            }
        }
    }

    private void cancelTaskByActIdComponentIndex(DelayKey delayKey, Map<Long, Map<Long, ScheduledTask>> tasks, Long actId, Long componentIndex) {
        if (closed) {
            return;
        }
        Map<Long, ScheduledTask> componentTask = tasks.get(actId);
        if (MapUtils.isNotEmpty(componentTask)) {
            ScheduledTask task = componentTask.get(componentIndex);
            if (task != null) {
                task.cancel();
                componentTask.remove(componentIndex);
                log.info("removed scheduled task: {} {} {}", delayKey, actId, componentIndex);

                if (componentTask.isEmpty()) {
                    tasks.remove(actId);
                }
            }
        }
    }

    private StringRedisTemplate getRedisTemplate(Long actId) {
        return redisGroupDao.getRedisTemplate(redisConfigManager.getGroupCode(actId));
    }

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setTaskScheduler(threadPoolTaskScheduler);
        this.taskRegistrar = taskRegistrar;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext().getParent() == null) {
            Date startTime = DateUtils.addSeconds(new Date(), 10);
            TASK_SCHEDULER.scheduleWithFixedDelay(() -> {
                if (taskRegistrar == null) {
                    return;
                }

                DELAY_COMPONENTS.forEachEntry(10, entry -> this.refreshDelayComponent(entry.getKey(), entry.getValue()));
            }, Instant.ofEpochMilli(startTime.getTime()), Duration.ofSeconds(60));
        }
    }

    @Order(0)
    @EventListener(ContextClosedEvent.class)
    public void onApplicationClosed(ContextClosedEvent event) {
        if (closed) {
            return;
        }

        closed = true;
        TASK_SCHEDULER.shutdown();
        DELAY_COMPONENTS.forEachValue(10, this::shutdownConsume);
    }
}
