package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Sets;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;


@Getter
@Setter
public class AwardUnicastComponent2Attr extends ComponentAttr {
    @ComponentAttrField(labelText = "业务id", dropDownSourceBeanClass = BizSource.class)
    private Long busiId;

    @ComponentAttrField(labelText = "奖池id", remark = "多个时用逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Set<Long> tAwardTskIds = Sets.newHashSet();
    /**
     * 空表示所有taskid下的奖品
     */
    @ComponentAttrField(labelText = "奖包id", remark = "空表示所有taskid下的奖品,多个时用逗号分隔",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Set<Long> tAwardPkgIds = Sets.newHashSet();

    @ComponentAttrField(labelText = "未中奖奖包id", remark = "多个时用逗号分隔，如命中，返回前端未中奖字段，显示为中间特别样式",
            subFields = {@SubField(fieldName = Constant.LIST_VALUE_TYPE, type = Long.class)})
    private Set<Long> tAwardPkgMissIds = Sets.newHashSet();

    @ComponentAttrField(labelText = "提醒类型",remark = "前端显示用")
    private String noticeType;

    @ComponentAttrField(labelText = "广播模板过滤弹幕游戏模板")
    private boolean filterDanmaku = true;

    @ComponentAttrField(labelText = "进程内延迟广播", remark = "单位：秒")
    private int delaySeconds = 0;

}
