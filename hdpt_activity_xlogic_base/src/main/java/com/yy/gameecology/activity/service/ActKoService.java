package com.yy.gameecology.activity.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.KoInfo;
import com.yy.gameecology.activity.bean.hdzt.ActivityInfoVo;
import com.yy.gameecology.activity.bean.hdzt.GetRankReq;
import com.yy.gameecology.activity.bean.hdzt.RankInfo;
import com.yy.gameecology.activity.bean.rank.KoRankItem;
import com.yy.gameecology.activity.bean.rank.RankItemBase;
import com.yy.gameecology.activity.bean.rank.TeamRankItem;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.activity.processor.ranking.impl.RankingAnyProcessor;
import com.yy.gameecology.activity.worker.timer.TimerSupport;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.utils.BeanUtil;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.hdzj.element.history.ActivityKoComponent;
import com.yy.gameecology.hdzj.element.history.attr.ActivityKoComponentAttr;
import com.yy.thrift.hdztranking.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Collections.singletonList;

/**
 * @Author: CXZ
 * @Desciption:
 * @Date: 2020/11/16 20:33
 * @Modified:
 */
@Service
public class ActKoService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());


    private static final String NAME_FORMAT = "%s:%s:%s:ko";
    private static final String DOING_METHOD = "doing";
    private static final String ACCOMPLISH_METHOD = "accomplish";
    private static final String SCRIPT_UPDATE_KO_DATA = "updateKo.lua";
    private static final String SCRIPT_GET_KO_DATA = "getKoData.lua";
    private static final String SCRIPT_MOVE_KO_DATA = "moveKo.lua";

    @Autowired
    private TimerSupport timerSupport;

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private CommonService commonService;

    @Autowired
    private RankingAnyProcessor anyProcessor;
    @Autowired
    private ActivityKoComponent koComponent;


    private static final String LOCK_NAME = "ko_lock";

    @Autowired
    private RedisConfigManager redisConfigManager;


    private int delaySeconds = 1;

    private String startTimeSub = "20:00:00";
    private String endTimeSub = "23:00:00";

    private Date startDay = DateUtil.getDate("2020-12-04 " + startTimeSub, DateUtil.DEFAULT_PATTERN);

    private Date endDay = DateUtil.getDate("2020-12-05 " + endTimeSub, DateUtil.DEFAULT_PATTERN);


    //@Scheduled(cron = "*/1 * * * * ?")
    public void work() {
        timerSupport.work(LOCK_NAME, 30, () -> {
            ko();
        });

    }


    public void ko() {

        List<ActivityInfoVo> activityInfoVos = hdztRankingThriftClient.queryEffectActInfos();
        if (CollectionUtils.isEmpty(activityInfoVos)) {
            return;
        }
        for (ActivityInfoVo activityInfoVo : activityInfoVos) {
            long actId = activityInfoVo.getActId();

            if (actId != 202012002L) {
                continue;
            }
            Date now = commonService.getNow(actId);
            if (!inKoDoingTime(actId, now)) {
                continue;
            }

            List<Long> rankIds = Lists.newArrayList(121L);
            Map<Long, RankingInfo> rankingInfoMap = hdztRankingThriftClient.queryRankConfig(actId, rankIds);
            if (MapUtils.isEmpty(rankingInfoMap)) {
                continue;
            }
            for (RankingInfo rankingInfo : rankingInfoMap.values()) {
                List<Long> koPhaseId = Lists.newArrayList(25L);
                long koAccomplishDurationTime = 15 * 60 * 1000L;
                long koStartScore = 200_000L;
                int maxKoCount = 1;
                long koAwardScore = 5L;
                ko(rankingInfo, koPhaseId, koAccomplishDurationTime, koStartScore, koAwardScore, maxKoCount);
            }
        }


    }

    /**
     * 本次ko结束时间早与阶段结束时间，所以积分增加由ko控制，不用严格以中台时间
     * 如果ko结算时间和阶段结束时间一样，会有临界问题
     *
     * @param rankingInfo
     * @param koPhaseId
     * @param koAccomplishDurationTime
     * @param koStartScore
     * @param koAwardScore
     * @param maxKoCount
     */
    private void ko(RankingInfo rankingInfo, List<Long> koPhaseId, long koAccomplishDurationTime,
                    long koStartScore, long koAwardScore, int maxKoCount) {

        long actId = rankingInfo.getActId();
        long rankId = rankingInfo.getRankingId();
        Date now = commonService.getNow(actId);
        RankingPhaseInfo currentPhaseInfo = rankingInfo.getCurrentPhase();
        String groupCode = redisConfigManager.getGroupCode(actId);


        long currentPhaseId = Optional.ofNullable(currentPhaseInfo)
                .map(RankingPhaseInfo::getPhaseId).orElse(0L);
        long timeType = rankingInfo.getTimeKey();

        //更新数据
        if (koPhaseId.contains(currentPhaseId)) {
            //时间key
            String timeKey = getTimeKey(now, timeType);

            long time = now.getTime();
            //查询pk数据
            PkInfo pkInfo = hdztRankingThriftClient.queryPhasePkgroup(actId, rankId, currentPhaseId, timeKey, timeKey, true, true, Maps.newHashMap());
            List<PkGroupItem> memberPkItems = Optional.ofNullable(pkInfo).map(PkInfo::getPkGroupItems).orElse(Collections.emptyList());
            Map<String, KoInfo> koInfoMap = memberPkItems.stream().map(PkGroupItem::getMemberPkItems)
                    .flatMap(Collection::stream)
                    .filter(item ->
                            CollectionUtils.isNotEmpty(item)
                                    && item.size() == 2
                                    && Math.abs(item.get(0).getScore() - item.get(1).getScore()) >= koStartScore
                    )
                    .map(item -> memberPkItems2KoInfo(item, time))
                    .collect(Collectors.toMap(KoInfo::getMemberKey, Function.identity()));

            String updateKey = getKey(actId, rankId, currentPhaseId, timeKey);
            if (koInfoMap.isEmpty()) {
                updateKoInfo(actId, updateKey, Maps.newHashMap(), 0L, 0L, 0);
            } else {

                List<String> accomplishKoList = updateKoInfo(actId, updateKey, koInfoMap, time, koAccomplishDurationTime, maxKoCount);
                //发送
                long busiId = 200;
                for (String accomplishKo : accomplishKoList) {
                    String seq = UUID.randomUUID().toString();

                    String accomplishMember = accomplishKo.split("_")[0];
                    Map<String, String> data = ImmutableMap.of("seq", seq,
                            "member", accomplishMember,
                            "score", koAwardScore + "");
                    String sign = "PW_KO_ITEM";
                    // now = commonService.getNow(actId);
                    //更新积分：更新之前做一次校验，避免出现临界问题
                    // if (inKoDoing(rankingInfo, currentPhaseId, now, timeKey)) {
                    Map<String, String> responseData = hdztRankingThriftClient.invokeRetry(busiId, actId, data, sign, 3);
                    if (MapUtils.isNotEmpty(responseData)) {
                        //转移数据
                        moveKoInfo(actId, updateKey, accomplishKo);
                        log.info("invoke info,seq:{},busiId:{},type:{},data:{},sign:{},response:{},accomplishKo:{}",
                                seq, busiId, actId, JSON.toJSONString(data), sign, responseData, accomplishKo);
                    } else {
                        //积分更新失败
                        log.error("invoke error,seq:{},busiId:{},type:{},data:{},sign:{},response:{}",
                                seq, busiId, actId, JSON.toJSONString(data), sign, responseData);
                    }
                   /* } else {
                        log.warn("ko  error,no in doing ,actId:{},rankId:{},phase:{},updateKey={},accomplishMember:{},now:{}",
                                actId, rankId, currentPhaseId, updateKey, accomplishMember,DateUtil.format(now));
                    }*/


                }

            }

        }


       /*
        long currentPhaseBeginTime = Optional.ofNullable(currentPhaseInfo)
                .map(RankingPhaseInfo::getBeginTime).orElse(now.getTime());
        //阶段已经开始的xiao
        long phaseStartHour = (currentPhaseBeginTime - now.getTime()) / (60 * 60 * 1000);
        //清除最近一个ko记录
        RankingPhaseInfo lastPhaseInfo = getLastPhaseInfo(rankingInfo.getPhasesMap(), now.getTime());

        String deleteLastKey = "";
        long deletePhaseId = 0L;
        //日榜和小时榜删除上一天或上个小时
        if (timeType == 1 && phaseStartHour > 24 || timeType == 2 && phaseStartHour > 1) {
            deletePhaseId = currentPhaseId;
            Date lastDate = timeType == 1 ? DateUtil.add(now, -1) : DateUtil.addMinutes(now, 60);
            deleteLastKey = getKey(actId, rankId, deletePhaseId, lastDate, timeType);
        } else if (lastPhaseInfo != null) {
            //阶段榜，并且存在上个阶段
            Date lastDate = new Date(lastPhaseInfo.getEndTime());
            deletePhaseId = lastPhaseInfo.getPhaseId();
            deleteLastKey = getKey(actId, rankId, deletePhaseId, lastDate, timeType);
        }
        //删除
        if (koPhaseId.contains(deletePhaseId)) {
            updateKoInfo(deleteLastKey, Maps.newHashMap(), 0L, 0L, 0);
        }*/
    }


    /**
     * 获取key
     *
     * @param actId
     * @param rankId
     * @param phaseId
     * @param dateStr
     * @return
     */
    private String getKey(long actId, long rankId, long phaseId, String dateStr) {
        if (StringUtil.isBlank(dateStr)) {
            dateStr = "_";
        }
        String name = String.format(NAME_FORMAT, rankId, phaseId, dateStr);
        return Const.addActivityPrefix(actId, name);
    }

    /**
     * 获取时间key，阶段榜是 "",日榜是 yyyyMMdd，小时榜是 yyyyMMddHH
     *
     * @param now
     * @param timeType
     * @return
     */
    private String getTimeKey(Date now, long timeType) {
        String timeKey = "";
        if (timeType == TimeKeyHelper.TIME_KEY_BY_DAY) {
            //日榜
            timeKey = DateUtil.format(now, DateUtil.PATTERN_TYPE2);
        } else if (timeType == TimeKeyHelper.TIME_KEY_BY_HOUR) {
            //小时榜
            timeKey = DateUtil.format(now, DateUtil.PATTERN_YYYYMMDD);
        }
        return timeKey;
    }

    /**
     * 获取上个阶段，没有上个阶段是null
     *
     * @param phasesMap
     * @param now
     * @return
     */
    private RankingPhaseInfo getLastPhaseInfo(Map<Long, RankingPhaseInfo> phasesMap, long now) {
        return phasesMap.values().stream()
                .sorted(Comparator.comparing(RankingPhaseInfo::getBeginTime))
                .filter(phaseInfo -> now > phaseInfo.getEndTime()).findFirst().orElse(null);
    }

    /**
     * 实体转换
     *
     * @param memberPkItems
     * @param time
     * @return
     */
    private KoInfo memberPkItems2KoInfo(List<GroupMemberItem> memberPkItems, long time) {
        //左边总是的
        if (memberPkItems.get(0).getRank() > memberPkItems.get(1).getRank()) {
            Collections.reverse(memberPkItems);
        }
        GroupMemberItem winnerItem = memberPkItems.get(0);
        GroupMemberItem failItem = memberPkItems.get(1);

        String memberKey = winnerItem.getMemberId() + "_" + failItem.getMemberId();
        Long diff = winnerItem.getScore() - failItem.getScore();

        KoInfo koInfo = new KoInfo();
        koInfo.setMemberKey(memberKey);
        koInfo.setDiff(diff);
        koInfo.setEndTime(time);

        koInfo.setWinnerMemberId(winnerItem.getMemberId());
        koInfo.setWinnerScore(winnerItem.getScore());
        koInfo.setWinnerRank(winnerItem.getRank());

        koInfo.setLoserMemberId(failItem.getMemberId());
        koInfo.setLoserScore(failItem.getScore());
        koInfo.setLoserRank(failItem.getRank());

        return koInfo;
    }

    /**
     * 更新ko数据并且获取完成ko的对象
     *
     * @param key
     * @param koInfoMap
     * @param nowTime
     * @param koAccomplishTime
     * @param maxKoCount
     * @return
     */
    private List<String> updateKoInfo(long actId, String key, Map<String, KoInfo> koInfoMap, long nowTime, long koAccomplishTime, int maxKoCount) {

        Map<String, String> dataMap = koInfoMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> JSON.toJSONString(entry.getValue())));
        String data = JSON.toJSONString(dataMap);
        //执行lua
        //完成ko的最晚开始时间
        long mixTime = nowTime - koAccomplishTime;
        //可能完成ko的最晚时间
        long possibleAccomplishKoLastTime = canAccomplishKoLastTime(new Date(nowTime), koAccomplishTime);

        String groupCode = redisConfigManager.getGroupCode(actId);

        String result = actRedisDao.executeLua(groupCode, SCRIPT_UPDATE_KO_DATA, String.class, singletonList(key),
                Lists.newArrayList(data, nowTime + "", mixTime + "", maxKoCount + "",
                        possibleAccomplishKoLastTime + "", 0 + ""));

        //返回数据处理
        JSONObject queryResultJson = JSONObject.parseObject(result);
        Object dataObject = queryResultJson.get("koAccomplish");
        List<String> accomplishKoList = Lists.newArrayList();
        if (dataObject != null && dataObject instanceof JSONArray) {
            accomplishKoList = ((JSONArray) dataObject).toJavaList(String.class);
        }
        log.info("updateKoInfo info :{}", queryResultJson);

        return accomplishKoList;
    }

    private void moveKoInfo(long actId, String key, String memberKey) {

        String result = actRedisDao.executeLua(redisConfigManager.getGroupCode(actId), SCRIPT_MOVE_KO_DATA, String.class, singletonList(key),
                singletonList(memberKey));

        //返回数据处理
        JSONObject queryResultJson = JSONObject.parseObject(result);
        int code = queryResultJson.getIntValue("code");
        if (code != 0) {
            log.error("moveKoInfo error,key:{},memberKey:{},result:{}", key, memberKey, result);
        } else {
            log.info("moveKoInfo info,key:{},memberKey:{},result:{}", key, memberKey, result);
        }

    }


    /**
     * 查询ko记录
     *
     * @param actId
     * @param rankId
     * @param phaseId
     * @param dateStr
     * @param queryDoing
     * @return
     */
    public List<KoInfo> queryKoInfos(long actId, long rankId, long phaseId, String dateStr,
                                     boolean queryDoing) {

        String key = getKey(actId, rankId, phaseId, dateStr);
        List<KoInfo> koInfos = Lists.newArrayList();

        String method = ACCOMPLISH_METHOD;
        if (queryDoing) {
            method = DOING_METHOD;
        }
        String groupCode = redisConfigManager.getGroupCode(actId);
        String queryResultString = actRedisDao.executeLua(groupCode, SCRIPT_GET_KO_DATA, String.class, singletonList(key), singletonList(method));

        //返回数据处理
        JSONObject queryResultJson = JSONObject.parseObject(queryResultString);
        Object dataObject = queryResultJson.get("data");
        if (dataObject != null && dataObject instanceof JSONArray) {
            koInfos = ((JSONArray) dataObject).stream().map(data -> JSONObject.parseObject(String.valueOf(data), KoInfo.class)).collect(Collectors.toList());
        }

        String message = queryResultJson.getString("message");
        if (StringUtils.isNotBlank(message) && koInfos.isEmpty()) {
            log.error("getKoInfos error,actId:{},rankId:{},phaseId:{},dateStr:{},queryDoing:{},message:{}",
                    actId, rankId, phaseId, dateStr, queryDoing, message);
        }
        return koInfos;
    }

    /**
     * 查询ko记录
     *
     * @param actId
     * @param rankId
     * @param phaseId
     * @param dateStr
     * @param type          2 = 正在ko的记录，其他是完成ko记录
     * @param limitCount
     * @param pointMemberId
     * @return
     */
    public RankInfo queryKoInfos(long actId, long rankId, long phaseId, String dateStr,
                                 int type, int limitCount, String pointMemberId) {

        RankInfo rankInfo = new RankInfo();
        ActivityKoComponentAttr attr = koComponent.getComponentAttr(actId, 1);
        if (attr == null) {
            return rankInfo;
        }
        long koAccomplishDurationTime = attr.getKoAccomplishDurationTime();

        RankingInfo rankingInfo = hdztRankingThriftClient.queryRankConfig(actId, rankId);

        if (dateStr == null) {
            dateStr = "";
        }

        boolean queryDoing = type == 2;
        Date now = commonService.getNow(actId);
        //拒绝不在ko内的查询，减少redis访问
        if (queryDoing && !koComponent.inKoDoingTime(attr, now)) {
            return rankInfo;
        }

        List<KoInfo> koInfos = queryKoInfos(actId, rankId, phaseId, dateStr, queryDoing);
        if (!koInfos.isEmpty()) {

            //可能完成ko的最晚时间
            long possibleAccomplishKoLastTime = koComponent.canAccomplishKoLastTime(now, koAccomplishDurationTime, koComponent.getEndTimeSub(attr, now));

            Set<String> members = koInfos.stream()
                    .filter(koInfo -> !queryDoing || koInfo.getStartTime() <= possibleAccomplishKoLastTime)
                    .map(info -> Arrays.asList(info.getMemberKey().split("_")))
                    .flatMap(Collection::stream).collect(Collectors.toSet());
            //转成成rank，用来查询用户信息
            List<Rank> ranks = members.stream().map(member -> {
                Rank rank = new Rank();
                rank.setMember(member);
                rank.setScore(1);
                rank.setScore(1);
                return rank;
            }).collect(Collectors.toList());

            GetRankReq getRankReq = new GetRankReq();
            getRankReq.setRankId(rankId);
            getRankReq.setActId(actId);
            getRankReq.setShowZeroItem(false);
            //查询用户信息
            List<Object> rankInfos = anyProcessor.getRankInfo(getRankReq, rankingInfo, ranks, Maps.newHashMap());
            Map<String, RankItemBase> rankItemMap = rankInfos.stream().map(rank -> (RankItemBase) rank)
                    .collect(Collectors.toMap(RankItemBase::getKey, Function.identity()));


            List<KoRankItem> koRankItems = koInfos.stream()
                    .map(koInfo -> toKoRankItem(koInfo, rankItemMap, now.getTime(), koAccomplishDurationTime, queryDoing, attr.getKoAwardScore()))
                    //查询进行中ko 倒计时必须大于1秒
                    .filter(koRankItem -> !queryDoing || koRankItem.getCountDown() >= 1000L)
                    .collect(Collectors.toList());

            //找到指定成员
            if (StringUtils.isNotBlank(pointMemberId)) {
                KoRankItem pointMember = koInfos.stream()
                        .filter(info -> pointMemberId.equals(info.getWinnerMemberId()) || pointMemberId.equals(info.getLoserMemberId()))
                        .map(koInfo -> toKoRankItem(koInfo, rankItemMap, now.getTime(), koAccomplishDurationTime, queryDoing, attr.getKoAwardScore()))
                        .filter(koRankItem -> !queryDoing || koRankItem.getCountDown() >= 1000L)
                        .findFirst().orElse(null);
                rankInfo.setPointedMember(pointMember);
            }


            int total = koRankItems.size();
            if (total > limitCount) {
                koRankItems = koRankItems.subList(0, limitCount);
            }
            rankInfo.setTotalCount(total + 0L);
            rankInfo.setList(koRankItems);
            //拒绝不在ko内的查询，提高时效性
            if (queryDoing && !koComponent.inKoDoingTime(attr, commonService.getNow(actId))) {
                return new RankInfo();
            }
        }

        return rankInfo;
    }

    /**
     * 实体转换
     *
     * @param koInfo
     * @param rankItemMap
     * @param now
     * @param koAccomplishDurationTime
     * @return
     */
    private KoRankItem toKoRankItem(KoInfo koInfo, Map<String, RankItemBase> rankItemMap, long now
            , long koAccomplishDurationTime, boolean queryDoing, long awardScore) {
        KoRankItem koRankItem = new KoRankItem();
        RankItemBase winner = BeanUtil.copy(rankItemMap.get(koInfo.getWinnerMemberId()));
        RankItemBase loser = BeanUtil.copy(rankItemMap.get(koInfo.getLoserMemberId()));

        winner.setRank(koInfo.getWinnerRank());
        winner.setValue(koInfo.getWinnerScore());
        loser.setRank(koInfo.getLoserRank());
        loser.setValue(koInfo.getLoserScore());

        // 补充胜负状态
        if (loser instanceof TeamRankItem && !queryDoing) {
            ((TeamRankItem) loser).setWin(-1);
        }

        if (winner instanceof TeamRankItem && !queryDoing) {
            ((TeamRankItem) winner).setWin(1);

            // 查询的是ko完成的，需要展示奖励的积分数
            ((TeamRankItem) winner).setAddition(awardScore);
        }

        koRankItem.setWinner(winner);
        koRankItem.setLoser(loser);
        koRankItem.setDiff(koInfo.getDiff());
        koRankItem.setStartTime(koInfo.getStartTime());
        koRankItem.setEndTime(koInfo.getEndTime());

        if (queryDoing) {
            long countDown = koAccomplishDurationTime - (now - koInfo.getStartTime());
            koRankItem.setCountDown(countDown);
        } else {
            koRankItem.setCountDown(-1L);
        }

        return koRankItem;
    }

    /**
     * 是否处于ko状态
     *
     * @param rankingInfo
     * @param phaseId
     * @param now
     * @param dateStr
     * @return
     */
    private boolean inKoDoing(RankingInfo rankingInfo, long phaseId, Date now, String dateStr) {

        String timeKey = getTimeKey(now, rankingInfo.getTimeKey());
        Long currentPhaseId = Optional.ofNullable(rankingInfo).map(RankingInfo::getCurrentPhase).map(RankingPhaseInfo::getPhaseId).orElse(0L);
        if (currentPhaseId <= 0 || currentPhaseId != phaseId || !timeKey.equals(dateStr) || !inKoDoingTime(rankingInfo.getActId(), now)) {
            return false;
        }

        return true;
    }

    private long canAccomplishKoLastTime(Date now, long koAccomplishDurationTime) {

        return DateUtil.getDate(DateUtil.format(now, "yyyy-MM-dd " + endTimeSub)).getTime() - koAccomplishDurationTime;

    }


    /**
     * 是否处于ko时间
     *
     * @param actId
     * @param now
     * @return
     */
    private boolean inKoDoingTime(long actId, Date now) {


        Date startTime = DateUtil.getDate(DateUtil.format(now, "yyyy-MM-dd " + startTimeSub));
        Date endTime = DateUtil.getDate(DateUtil.format(now, "yyyy-MM-dd " + endTimeSub));

        if (startDay.after(now) || endDay.before(now) || startTime.after(now) || endTime.before(now)) {
            return false;
        }

        return true;
    }


}
