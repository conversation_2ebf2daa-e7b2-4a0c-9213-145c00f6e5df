package com.yy.gameecology.activity.service;

import com.google.common.collect.Lists;
import com.yy.boot.starter.threadpool.ThreadPoolManager;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.consts.ThreadPoolNames;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * desc:请求归并
 *
 * <AUTHOR>
 * @date 2024-11-04 16:54
 **/
@Service
public class AggregationService {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private static final String AGGREGATION_SCRIPT = "aggregation_invoke.lua";

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private ThreadPoolManager threadPoolManager;

    @Autowired
    private ActRedisGroupDao actRedisDao;

    /**
     * 请求聚合，适用于异步执行控制并发调用方法，并且中间少调用了，并不会影响结果，减少中间无效调用
     * @param actId 活动id
     * @param key 去重聚合key
     * @param aggregationSeconds 聚合时间
     * @param runnable 执行方法
     */
    public void invoke(long actId, String key, int aggregationSeconds, Runnable runnable) {
        long nextInvokeSeconds = getNextInvokeSeconds(actId, key, aggregationSeconds);
        if (nextInvokeSeconds == 0) {
            log.info("aggregation invoke fire,actId:{},key:{},next seconds:{}", actId, key, nextInvokeSeconds);
            runnable.run();
        } else if (nextInvokeSeconds > 0) {
            log.info("aggregation invoke delay,actId:{},key:{},next seconds:{}", actId, key, nextInvokeSeconds);

            threadPoolManager.getScheduledThreadPool(ThreadPoolNames.AGGREGATION_SCHEDULE).schedule(runnable
                    , nextInvokeSeconds, TimeUnit.SECONDS);
        } else {
            log.info("aggregation invoke drop,actId:{},key:{},next seconds:{}", actId, key, nextInvokeSeconds);
        }
    }

    /**
     * 或取下次调用时间
     *
     * @param actId              活动id
     * @param aggregationKey     频道id
     * @param aggregationSeconds 子频道id，如果顶级频道广播，则子频道id传0
     * @return -1===丢弃本次更新，无需更新;  0 ===立即更新  ; > 0 , x秒后更新
     */
    public long getNextInvokeSeconds(long actId, String aggregationKey, int aggregationSeconds) {
        // 如果set不成功则丢弃本次更新，如果set成功了,并且和上次时间比较，超过了x秒，则马上更新，否则延迟 x - (当前时间-上次更新时间)更新
        String key = Const.addActivityPrefix(actId, "aggregation:" + aggregationKey);
        List<String> para = Lists.newArrayList();
        para.add(Convert.toString(DateUtil.getSeconds()));
        para.add(Convert.toString(aggregationSeconds));
        String groupCode = redisConfigManager.getGroupCode(actId);
        return actRedisDao.executeLua(groupCode, AGGREGATION_SCRIPT, Long.class, Collections.singletonList(key), para);
    }
}
