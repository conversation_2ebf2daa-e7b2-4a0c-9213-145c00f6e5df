package com.yy.gameecology.activity.service.common;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableListMultimap;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.hdzj.bean.AwardVO;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/7/7
 */
@Service
@Slf4j
public class AwardService {

    private ImmutableListMultimap<String, AwardConfig> awardConfigMap;

    @Autowired
    private ActRedisGroupDao redis;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @PostConstruct
    @NeedRecycle(author = "zengyuan", notRecycle = true)
    public void init() {
        ImmutableListMultimap.Builder<String, AwardConfig> builder = ImmutableListMultimap.builder();
        awardConfigMap = builder.build();
    }

    public Response<List<AwardVO>> award(String awardKey, String source, long uid) {
        log.info("award starting,key:{},source:{},uid:{}", awardKey, source, uid);
        return doAward(awardKey, source, uid);
    }

    public Response<List<AwardVO>> award(List<AwardConfig> configs,String source, long uid){
        List<AwardVO> vos = doAward(configs, source);
        log.info("award is successful,source:{},uid:{},awardSize:{}", source, uid, vos.size());
        return Response.success(vos);
    }

    private Response<List<AwardVO>> doAward(String awardKey, String source, long uid) {
        ImmutableList<AwardConfig> configs = awardConfigMap.get(awardKey);
        if (CollectionUtils.isEmpty(configs)) {
            log.warn("award key not exist,awardKey:{},source:{},uid:{}", awardKey, source, uid);
            return Response.fail(2, "礼物抽奖模型未设置");
        }
        List<AwardVO> vos = doAward(configs, source);
        log.info("award is successful,awardKey:{},source:{},uid:{},awardSize:{}", awardKey, source, uid, vos.size());
        return Response.success(vos);
    }

    private List<AwardVO> doAward(List<AwardConfig> configs, String source) {
        List<AwardVO> vos = Lists.newArrayList();
        configs.forEach(cf -> vos.addAll(doAward(cf, source)));
        return vos;
    }

    private List<? extends AwardVO> doAward(AwardConfig cf, String source) {
        String groupCode = redisConfigManager.getGroupCode(cf.getActId());
        return cf.getPrizes().stream()
                .map(prizeVO -> doAward(groupCode, prizeVO, cf.getAwardType(), source))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    private Optional<AwardVO> doAward(String groupCode, PrizeVO prize, String awardType, String source) {
        if (prize == null) {
            return Optional.empty();
        }
        if (StringUtils.isEmpty(awardType)) {
            return Optional.empty();
        }
        if (AwardType.ALL.getType().equalsIgnoreCase(awardType)) {
            // all 忽略概率和数量
            AwardVO awardVO = buildAwardVO(prize, prize.getMin());
            return Optional.ofNullable(awardVO);
        }
        if (AwardType.RandomByMaxLimit.getType().equalsIgnoreCase(awardType)) {
            //忽略概率，随机数量
            int rdPrizes = prize.getMin() + new Random().nextInt(prize.getMax() - prize.getMin() + 1);
            List<String> args = Lists.newArrayList(String.valueOf(prize.getMin()), String.valueOf(rdPrizes), String.valueOf(prize.getLimit()));
            long count = redis.executeLua(groupCode,"award_rd_limit.lua", Long.class, Lists.newArrayList(source), args);
            AwardVO awardVO = buildAwardVO(prize, (int) count);
            return Optional.ofNullable(awardVO);
        }
        log.warn("awardType not exist,awardType:{},source:{}", awardType, source);
        return Optional.empty();
    }

    private AwardVO buildAwardVO(PrizeVO prize, int count) {
        return AwardVO.builder()
                .name("")
                .taskId(prize.getTaskId())
                .packageId(prize.getPackageId())
                .count(count)
                .build();
    }


    @AllArgsConstructor
    @Getter
    public enum AwardType {
        /**
         * 发奖模式-忽略概率和数量
         */
        ALL("all"),
        /**
         * 忽略概率，随机数量
         */
        RandomByMaxLimit("RandomByMaxLimit");
        private final String type;


    }


}

















