package com.yy.gameecology.activity.client.thrift;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.fostress.api.RetryStrategy;
import com.yy.fostress.retry.annotation.DistributedRetry;
import com.yy.fostress.sitter.utils.RetryUtils;
import com.yy.gameecology.activity.retry.BatchWelfareRetryCallback;
import com.yy.gameecology.common.annotation.Cached;
import com.yy.gameecology.common.annotation.Report;
import com.yy.gameecology.common.consts.CacheTimeout;
import com.yy.gameecology.common.consts.WelfareCode;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.*;
import com.yy.thrift.hdztaward.*;
import com.yy.thrift.hdztranking.BusiId;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Component
public class HdztAwardServiceClient {

    private static final Logger log = LoggerFactory.getLogger(HdztAwardServiceClient.class);

    public static final String CP_UID = "cpUid";

    public static final String VIEW_EXT = "viewExt";
    /**
     * 显示去重标识
     */
    public static final String DUP_SHOW_SEQ = "dupShowSeq";

    //神豪uid|主播uid 全服记录显示 神豪&&主播
    public static final String CP_MEMBER = "cpMember";

    // 用上面的 DUP_SHOW_SEQ
    //神豪主播发了两个有两条记录 用这个去做uniq
//    public static final String CP_UNIQ_SEQ = "cpUniSeq";

    /**
     * 发奖的场景，可以通过scene来查询奖励记录
     * scene必须为如下格式："{actId}|xxx"，前端接口查询的时候不需要“{actId}|”前缀
     */
    public static final String AWARD_SCENE = "awardScene";

    private static final String ERROR_MSG = "历史环境禁止HdztAwardServiceClient写入";

    /**
     * 禁止直接引用face
     */
    @Reference(protocol = "nythrift", owner = "${thrift.client.hdztaward.s2sname}", registry = "consumer-reg", timeout = 10000, parameters = {"threads", "10"})
    private HdztAwardService.Iface proxy;

    @Reference(protocol = "nythrift", owner = "${thrift.client.hdztaward.s2sname}", registry = "consumer-reg", timeout = 10000, parameters = {"threads", "10"}
            , retries = 2 , cluster = "failover")
    private HdztAwardService.Iface readProxy;

    public HdztAwardService.Iface getProxy(boolean write) {
        if (write && SysEvHelper.checkHistory(ERROR_MSG, true)) {
            throw new RuntimeException(ERROR_MSG);
        }
        if(!write) {
            return readProxy;
        }
        return proxy;
    }

    public Map<String, String> invoke(long busiId, long type, Map<String, String> data, String sign) {
        Clock clock = new Clock();
        try {
            SimpleResult simpleResult = getProxy(true).invoke(busiId, type, data, sign);
            if (simpleResult == null || simpleResult.getCode() != 0) {
                log.error("invoke error,busiId:{},type:{},data:{},res:{}", busiId, type, data, simpleResult);
            }
            return simpleResult.getData();
        } catch (Exception e) {
            log.error("invoke busi:{} sign:{} err:{} {}", busiId, sign, e.getMessage(), clock.tag(), e);
            return null;
        }
    }

    /**
     * @param time   格式 yyyy-MM-dd HH:mm:ss
     * @param busiId 业务编码
     * @param uid    用户uid
     * @param taskId taskId
     * @param count  次数
     * @param bingo  必中的packageId
     * @param seq    请求序列
     * @return
     */
    @Report
    public BatchLotteryResult doLottery(String time, long busiId, long uid, long taskId, int count, long bingo, String seq) {
        return doLottery(time, busiId, uid, taskId, count, bingo, seq, Collections.emptyMap());
    }

    /**
     * @param time   格式 yyyy-MM-dd HH:mm:ss
     * @param busiId 业务编码
     * @param uid    用户uid
     * @param taskId taskId
     * @param count  次数
     * @param bingo  必中的packageId
     * @param seq    请求序列
     * @param extData  拓展参数
     * @return
     */
    @Report
    public BatchLotteryResult doLottery(String time, long busiId, long uid, long taskId, int count, long bingo, String seq, Map<String, String> extData) {
        Clock clock = new Clock();
        try {
            BatchLotteryRequest request = new BatchLotteryRequest();
            request.setBusiId(busiId);
            request.setUid(uid);
            request.setTaskId(taskId);
            request.setSeq(seq);
            Map<Integer, Long> assignHit = new HashMap<>(4);
            if (bingo > 0) {
                assignHit.put(1, bingo);
                request.setAssignHit(assignHit);
            }

            request.setIp(SystemUtil.getIp());
            request.setTimestamp(time);
            request.setLotteryCount(count);
            if (extData != null) {
                request.setExtData(extData);
            }

            BatchLotteryResult ret = getProxy(true).batchLottery(request);
            log.info("doLottery busi:{} uid:{} task:{} count:{} bingo:{} seq:{} ret:{} {}", busiId, uid, taskId, count, bingo, seq, ret, clock.tag());
            return ret;
        } catch (Exception e) {
            log.error("doLottery busi:{} uid:{} task:{} count:{} bingo:{} seq:{} err:{} {}", busiId, uid, taskId, count, bingo, seq, e.getMessage(), clock.tag(), e);
            return null;
        }
    }

    @Report
    public BatchWelfareResult doWelfare(String time, long busiId, long uid, long taskId, int count, long packageId, String seq, Map<String, String> extData, long retry) {
        log.info("begin doWelfare time:{},busiId:{},uid:{},taskId:{},count:{},pacakgeId:{},seq:{},extData:{},retry:{}"
                , time, busiId, uid, taskId, count, packageId, seq, extData, retry);

        while (retry-- > 0) {
            try {
                BatchWelfareResult result = this.doWelfare(time, busiId, uid, taskId, count, packageId, seq, extData);
                if (result != null && result.getCode() == 0) {
                    log.info("doBatchWelfare with retry ok, seq:{}", seq);
                    return result;
                } else {
                    log.error("doWelfare error,time:{},busiId:{},uid:{},taskId:{},count:{},packageId:{},seq:{},retry:{},rsp:{}", time, busiId, uid, taskId, count, packageId, seq, retry, result);
                }
                if (retry > 0) {
                    Thread.sleep(300);
                }
            } catch (Exception e) {
                log.warn("doBatchWelfare exception:" + e, e);
            }
        }
        return null;
    }

    @Report
    public BatchWelfareResult doWelfare(String time, long busiId, long uid, long taskId, int count, long packageId, String seq, long retry) {
        return doWelfare(time, busiId, uid, taskId, count, packageId, seq, Collections.emptyMap(), retry);
    }

    @Report
    public BatchWelfareResult doWelfare(String time, long busiId, long uid, long taskId, int count, long packageId, String seq, Map<String, String> extData) {
        Clock clock = new Clock();
        try {
            BatchWelfareRequest request = new BatchWelfareRequest();
            request.setBusiId(busiId);
            request.setUid(uid);
            request.setTaskId(taskId);
            request.setSeq(seq);
            Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap();
            Map<Long, Integer> packageIdCount = Maps.newHashMap();
            packageIdCount.put(packageId, count);
            taskPackageIds.put(taskId, packageIdCount);
            request.setTaskPackageIds(taskPackageIds);

            request.setIp(SystemUtil.getIp());
            request.setTimestamp(time);
            request.setExtData(extData);

            BatchWelfareResult ret = getProxy(true).batchWelfare(request);
            log.info("doWelfare busi:{} uid:{} task:{} count:{} packageId:{} seq:{} ret:{} {}", busiId, uid, taskId, count, packageId, seq, ret, clock.tag());
            return ret;
        } catch (Exception e) {
            log.error("doWelfare busi:{} uid:{} task:{} count:{} packageId:{} seq:{} err:{} {}", busiId, uid, taskId, count, packageId, seq, e.getMessage(), clock.tag(), e);
            return null;
        }
    }

    @Report
    public BatchWelfareResult doWelfare(String time, long busiId, long uid, long taskId, int count, long packageId, String seq) {
        return doWelfare(time, busiId, uid, taskId, count, packageId, seq, Collections.emptyMap());
    }

    @Report
    public BatchWelfareResult doWelfare(long busiId, long uid, long taskId, int count, long packageId, String seq) {
        return doWelfare(DateUtil.getNowYyyyMMddHHmmss(), busiId, uid, taskId, count, packageId, seq);
    }

    @Report
    @DistributedRetry
    public void doWelfareV2(String time, long busiId, long uid, long taskId, int count, long packageId, String seq, Map<String, String> extData) {
        Map<Long, Map<Long, Integer>> taskPackageIds = Maps.newHashMap();
        Map<Long, Integer> packageIdCount = Maps.newHashMap();
        packageIdCount.put(packageId, count);
        taskPackageIds.put(taskId, packageIdCount);

        BatchWelfareResult ret = this.doBatchWelfare(time, busiId, uid, taskId, taskPackageIds, seq, extData);
        if (ret == null || ret.code != 0) {
            throw new RuntimeException("reword result code not zero");
        }
    }

    @Report
    public BatchLotteryResult doBatchLottery(String seq, long busiId, long uid, long taskId) {
        return doBatchLottery(seq, busiId, uid, taskId, 1, 1);
    }

    @Report
    public BatchLotteryResult doBatchLottery(String seq, long busiId, long uid, long taskId, int retry) {
        return doBatchLottery(seq, busiId, uid, taskId, 1, retry);
    }

    @Report
    public BatchLotteryResult doBatchLottery(String seq, long busiId, long uid, long taskId, int lotteryCount, int retry) {
        return doBatchLottery(seq, busiId, uid, taskId, lotteryCount, DateUtil.today(), null, SystemUtil.getIp(), retry);
    }

    @Report
    public BatchLotteryResult doBatchLottery(String seq, long busiId, long uid, long taskId, int lotteryCount, Map<Integer, Long> assignHit, Map<String, String> extData, int retry) {
        return doBatchLottery(seq, busiId, uid, taskId, lotteryCount, DateUtil.today(), assignHit, SystemUtil.getIp(), null, null, Platform.UNKNOWN, 0, extData, null, retry);
    }

    public BatchLotteryResult doBatchLottery(String seq, long busiId, long uid, long taskId, int lotteryCount, String timestamp, int retry) {
        return doBatchLottery(seq, busiId, uid, taskId, lotteryCount, timestamp, null, SystemUtil.getIp(), retry);
    }

    @Report
    public BatchLotteryResult doBatchLottery(String seq, long busiId, long uid, long taskId, int lotteryCount, String timestamp, Map<Integer, Long> assignHit, String ip, int retry) {
        return doBatchLottery(seq, busiId, uid, taskId, lotteryCount, timestamp, assignHit, ip, null, null, Platform.UNKNOWN, 0, null, null, retry);
    }

    @Report
    public BatchLotteryResult doBatchLottery(String seq, long busiId, long uid, long taskId, int lotteryCount, String timestamp, Map<Integer, Long> assignHit,
                                             String ip, String mac, String key, Platform platform, long extLong, Map<String, String> extData, String sign, int retry) {
        // 本身1次 + 重试次数， 等于总次数
        int count = retry + 1;
        Clock clock = new Clock();
        BatchLotteryResult response = null;
        BatchLotteryRequest request = new BatchLotteryRequest();
        Throwable le = null;
        while (count-- > 0) {
            try {
                request.setSeq(seq);
                request.setBusiId(busiId);
                request.setUid(uid);
                request.setTaskId(taskId);
                request.setKey(key);
                request.setLotteryCount(lotteryCount);
                request.setAssignHit(assignHit);
                request.setPlatform(platform);
                request.setTimestamp(timestamp);
                request.setIp(ip);
                request.setMac(mac);
                request.setExtLong(extLong);
                request.setExtData(extData);
                request.setSign(sign);
                response = getProxy(true).batchLottery(request);
                log.info("doBatchLottery done@inx:{}, request:{}, response:{} {}", count + 1, request, response, clock.tag());
                return response;
            } catch (Throwable e) {
                le = e;
                log.warn("doBatchLottery exception@inx:{}, request:{}, err:{} {}", count + 1, request, e.getMessage(), clock.tag());
                if (retry > 0) {
                    SysEvHelper.waiting(300);
                }
            }
        }

        log.error("doBatchLottery fail@retry:{}, request:{}, err:{} {}", retry + 1, request, le.getMessage(), clock.tag(), le);
        return null;
    }

    @Report
    public BatchWelfareResult doBatchWelfare(String time, long busiId, long uid, long taskId, Map<Long, Map<Long, Integer>> taskPackageIds, String seq, long retry, Map<String, String> extData) {
        BatchWelfareResult result = null;
        while (retry-- > 0) {
            try {
                result = this.doBatchWelfare(time, busiId, uid, taskId, taskPackageIds, seq, extData);
                if (result != null && result.getCode() == 0) {
                    log.info("doBatchWelfare with busiId:{}, uid:{}, taskId:{}, taskPackageIds:{}, seq:{}", busiId, uid, taskId, JSON.toJSONString(taskPackageIds), seq);
                    return result;
                }
                if (retry > 0) {
                    Thread.sleep(300);
                }
            } catch (Exception e) {
                log.warn("doBatchWelfare exception:" + e, e);
            }
        }
        return result;
    }

    @Report
    public BatchWelfareResult doBatchWelfare(String time, long busiId, long uid, long taskId, Map<Long, Map<Long, Integer>> taskPackageIds, String seq, Map<String, String> extData) {
        Clock clock = new Clock();
        try {
            BatchWelfareRequest request = new BatchWelfareRequest();
            request.setBusiId(busiId);
            request.setUid(uid);
            request.setTaskId(taskId);
            request.setSeq(seq);
            request.setTaskPackageIds(taskPackageIds);
            request.setIp(SystemUtil.getIp());
            request.setExtData(extData);
            request.setTimestamp(time);
            BatchWelfareResult ret = getProxy(true).batchWelfare(request);
            if (ret == null || ret.code != 0) {
                log.error("doBatchWelfare fail busi:{} uid:{} task:{} taskPackageIds:{} seq:{} ret:{} {}", busiId, uid, taskId, JSON.toJSONString(taskPackageIds), seq, ret, clock.tag());
            } else {
                log.info("doBatchWelfare busi:{} uid:{} task:{} taskPackageIds:{} seq:{} ret:{} {}", busiId, uid, taskId, JSON.toJSONString(taskPackageIds), seq, ret, clock.tag());
            }
            return ret;
        } catch (Throwable e) {
            log.error("doBatchWelfare busi:{} uid:{} task:{} taskPackageIds:{} seq:{} err:{} {}", busiId, uid, taskId, JSON.toJSONString(taskPackageIds), seq, e.getMessage(), clock.tag(), e);
            return null;
        }
    }

    @DistributedRetry
    @Report
    public void doBatchWelfareV2(String time, long busiId, long uid, long taskId, Map<Long, Map<Long, Integer>> taskPackageIds, String seq, Map<String, String> extData) {
        BatchWelfareResult result = doBatchWelfare(time, busiId, uid, taskId, taskPackageIds, seq, extData);
        if (result.code != 0) {
            throw new RuntimeException("reword result code not zero");
        }
    }

    @Report
    public void doBatchWelfareV2(String time, long busiId, long uid, long taskId, Map<Long, Map<Long, Integer>> taskPackageIds, String seq, int maxAttempts, Map<String, String> extData) {
        //本地直接调用，不支持重试
        if (SysEvHelper.isLocal()) {
            doBatchWelfare(time, busiId, uid, taskId, taskPackageIds, seq, extData);
            return;
        }

        RetryUtils.exec("batchWelfareRetryCallback", new BatchWelfareRetryCallback.BatchWelfareParams(time, busiId, uid, taskId, taskPackageIds, seq, extData), maxAttempts);
    }

    @Report
    public void doBatchWelfareV2(String time, long busiId, long uid, long taskId, Map<Long, Map<Long, Integer>> taskPackageIds, String seq, RetryStrategy retryStrategy, Map<String, String> extData) {
        //本地直接调用，不支持重试
        if (SysEvHelper.isLocal()) {
            doBatchWelfare(time, busiId, uid, taskId, taskPackageIds, seq, extData);
            return;
        }

        RetryUtils.exec("batchWelfareRetryCallback", new BatchWelfareRetryCallback.BatchWelfareParams(time, busiId, uid, taskId, taskPackageIds, seq, extData), retryStrategy);
    }

    @Cached(timeToLiveMillis = CacheTimeout.HDZT_AWARD_TASK)
    public Map<Long, AwardModelInfo> queryAwardTasks(long taskId) {
        return queryAwardTasks(taskId, true);
    }

    @Cached(timeToLiveMillis = CacheTimeout.HDZT_AWARD_TASK)
    public AwardModelInfo queryAwardTask(long taskId, long packageId) {
        var awardTasks = queryAwardTasks(taskId);
        return awardTasks == null ? null : awardTasks.get(packageId);
    }

    @Cached(timeToLiveMillis = CacheTimeout.HDZT_AWARD_TASK)
    public Map<Long, AwardModelInfo> queryAwardTasks(long taskId, boolean withPkItem) {
        QueryAwardTaskResult result = null;
        try {
            result = getProxy(false).queryAwardTasks(Lists.newArrayList(taskId), withPkItem);
        } catch (Exception e) {
            log.error("queryAwardTasks fail", e);
        }

        if (result == null || result.getCode() != 0) {
            return null;
        }

        Map<AwardTaskInfo, List<AwardModelInfo>> taskMaps = result.getTaskAwardModelInfoMap();
        if (!CollectionUtils.isEmpty(taskMaps)) {
            for (AwardTaskInfo task : taskMaps.keySet()) {
                List<AwardModelInfo> packageList = taskMaps.get(task);
                return packageList.stream().collect(Collectors.toMap(AwardModelInfo::getPackageId, Function.identity()));
            }
        }

        return Maps.newHashMap();
    }

    @Cached(timeToLiveMillis = CacheTimeout.HDZT_AWARD_TASK)
    public Map<Long, AwardModelInfo> queryAwardTasks(List<Long> taskIds, boolean withPkItem) {
        QueryAwardTaskResult result = null;
        try {
            result = getProxy(false).queryAwardTasks(taskIds, withPkItem);
        } catch (Exception e) {
            log.error("queryAwardTasks fail", e);
        }

        if (result == null || result.getCode() != 0) {
            return null;
        }

        Map<AwardTaskInfo, List<AwardModelInfo>> taskMaps = result.getTaskAwardModelInfoMap();
        Map<Long, AwardModelInfo> packageMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(taskMaps)) {
            for (AwardTaskInfo task : taskMaps.keySet()) {
                List<AwardModelInfo> packageList = taskMaps.get(task);
                packageMap.putAll(packageList.stream().collect(Collectors.toMap(AwardModelInfo::getPackageId, Function.identity())));
            }
        }
        return packageMap;
    }

    /**
     * 查询用户勋章等级
     *
     * @param uid
     * @param mid
     * @param map
     * @return
     */
    public int getMyNamePlateLevel(long uid, long mid, Map<String, String> map) {
        try {
            YYMedalInfoResult myNameplateLevel = getProxy(false).getMyNameplateLevel(uid, mid, map);
            Map<Long, String> data = myNameplateLevel.getData();
            String s = data.get(uid);
            return Convert.toInt(s);
        } catch (Exception e) {
            log.error("getMyNamePlateLevel e={} e", e.getMessage(), e);
        }
        return 0;
    }

    /**
     * 为指定成员发放奖品包
     */
    @Report
    public void doBatchWelfare(long uid, Map<Long, Map<Long, Integer>> taskPackageIds, String time, int retry, Map<String, String> extData) {
        String seq = java.util.UUID.randomUUID().toString();
        doBatchWelfare(seq, uid, taskPackageIds, time, retry, extData);
    }

    /**
     * 为指定成员发放奖品包
     */
    @Report
    public BatchWelfareResult doBatchWelfare(String seq, long uid, Map<Long, Map<Long, Integer>> taskPackageIds, String time, int retry, Map<String, String> extData) {
        long busiId = BusiId.GAME_ECOLOGY.getValue();
        BatchWelfareResult result = this.doBatchWelfare(time, busiId, uid, 0, taskPackageIds, seq, retry + 1, extData);
        if (result != null && result.getCode() == 0) {
            log.info("award ok@with busiId:{}, uid:{}, taskPackageIds:{}, seq:{}", busiId, uid, JSON.toJSONString(taskPackageIds), seq);
        } else if (result != null && result.getCode() == WelfareCode.NO_AWARD_DJ_5204) {
            log.warn("award ok@with busiId:{}, uid:{}, taskPackageIds:{}, seq:{},ret:{}", busiId, uid, JSON.toJSONString(taskPackageIds), seq, JSON.toJSONString(result));
        } else {
            log.error("award fail@with busiId:{}, uid:{}, taskPackageIds:{}, seq:{},ret:{}", busiId, uid, JSON.toJSONString(taskPackageIds), seq, JSON.toJSONString(result));
        }

        return result;
    }

    /**
     * 查询task的余量，不可以对外面直接暴露接口，加1秒缓存避免大量网络请求中台
     *
     * @param taskId
     * @param data
     * @return
     */
    @Cached(timeToLiveMillis = 1000)
    public Map<Long, PackageLeftQuota> getTaskLeftQuota(Long taskId, Date data) {
        String day = DateUtil.format(data, DateUtil.PATTERN_TYPE2);
        try {
            TaskLeftQuotaResult taskLeftQuota = getProxy(false).getTaskLeftQuota(taskId, day);
            if (taskLeftQuota != null && taskLeftQuota.getCode() == 0) {
                log.info("getTaskLeftQuota ok@with taskId:{}, day:{}", taskId, day);
                return taskLeftQuota.getQuotas();
            } else {
                log.error("getTaskLeftQuota error@with taskId:{}, day:{} result:{}", taskId, day, JSON.toJSONString(taskLeftQuota));
            }
        } catch (Exception e) {
            log.error("getTaskLeftQuota error@with taskId:{}, day:{} {}", taskId, day, e.getMessage(), e);

        }
        return null;
    }

    public Map<Long, PackageLeftQuota> getTaskLeftQuota2(Long taskId, Date data) {
        String day = DateUtil.format(data, DateUtil.PATTERN_TYPE2);
        try {
            TaskLeftQuotaResult taskLeftQuota = getProxy(false).getTaskLeftQuota(taskId, day);
            if (taskLeftQuota != null && taskLeftQuota.getCode() == 0) {
                log.info("getTaskLeftQuota ok@with taskId:{}, day:{}", taskId, day);
                return taskLeftQuota.getQuotas();
            } else {
                log.error("getTaskLeftQuota error@with taskId:{}, day:{} result:{}", taskId, day, JSON.toJSONString(taskLeftQuota));
            }
        } catch (Exception e) {
            log.error("getTaskLeftQuota error@with taskId:{}, day:{} {}", taskId, day, e.getMessage(), e);

        }
        return null;
    }

    /**
     * 获取用户的中奖记录
     * busiId - 业务端标志（400:宝贝, 500：交友, 600:约战）
     * uid - 用户标志
     * taskList - 抽奖任务列表
     *
     * @param busiId
     * @param uid
     * @param taskList
     * @param packageIds
     */
    @Report
    public AwardRecordResult getUserAwardRecordListEx(long busiId, long uid, List<Integer> taskList, List<Long> packageIds) throws TException {
        return getUserAwardRecordListEx(busiId, uid, taskList, packageIds, true);
    }

    /**
     * 获取用户的中奖记录
     * busiId - 业务端标志（400:宝贝, 500：交友, 600:约战）
     * uid - 用户标志
     * taskList - 抽奖任务列表
     * isLimit - 限制返回数量，默认限制,谨慎使用
     *
     * @param busiId
     * @param uid
     * @param taskList
     * @param packageIds
     * @param isLimit
     */
    @Report
    public AwardRecordResult getUserAwardRecordListEx(long busiId, long uid, List<Integer> taskList, List<Long> packageIds, boolean isLimit) throws TException {
        return getProxy(false).getUserAwardRecordListEx(busiId, uid, taskList, packageIds, isLimit);
    }

    @Report
    public AwardIssueRecordResult queryUserAwardIssues(long busiId, long uid, Integer taskId, List<Long> packageIds)  {
        try{
            AwardIssueRecordResult result = getProxy(false).queryUserAwardIssues(busiId,uid,taskId,packageIds);
            if(result.getRetCode()!=0){
                log.error("queryUserAwardIssues error, uid:{} ,taskId:{}, packageIds:{} retcode:{}", uid, taskId, JsonUtil.toJson(packageIds),result.getRetCode());
            }
            return result;

        }catch (Exception e) {
            log.error("queryUserAwardIssues exception, uid:{} ,taskId:{}, packageIds:{} err:{} {}", uid, taskId, JsonUtil.toJson(packageIds),e.getMessage(), e);
            return null;
        }

    }
    
    public AwardRecordResult getAwardRecordByScene(long busiId, long uid, String scene, int size) {
        try {
            var result = getProxy(false).getAwardRecordByScene(busiId, uid, scene, size);
            if (result.getRetCode() != 0) {
                log.error("getAwardRecordByScene error, uid:{}, scene:{} retCode:{}", uid, scene, result.retCode);
            }

            return result;
        } catch (Exception e) {
            log.error("getAwardRecordByScene exception, uid:{} ,scene:{} err:{}", uid, scene, e.getMessage(), e);
        }

        return null;
    }

    /**
     * 获取奖包里奖项配置的总发放数量
     * @param awardPackage
     * @return
     */
    public static int getAwardCount(AwardModelInfo awardPackage) {
        if (awardPackage == null || org.apache.commons.collections.CollectionUtils.isEmpty(awardPackage.awardPackageItemInfos)) {
            return 0;
        }

        return awardPackage.getAwardPackageItemInfos().stream().map(AwardPackageItemInfo::getGiftNum).reduce(Integer::sum).orElse(1);
    }

    /**
     * 获取奖励数量：中控数量 * 奖项中配置的数量
     * @param awardPackage 奖包（包含奖项）
     * @param num 中控发放的数量
     * @return
     */
    public static int getAwardCount(AwardModelInfo awardPackage, int num) {
        return getAwardCount(awardPackage) * num;
    }

    /**
     * 根据奖包配置拼接奖励信息详情：粉丝票5个
     * @param awardPackage 奖包（包含奖项）
     * @param num 中控发放的数量
     * @return
     */
    public static String getAwardDetail(AwardModelInfo awardPackage, int num) {
        return awardPackage.getPackageName() + getAwardCount(awardPackage, num) + awardPackage.getUnit();
    }
}
