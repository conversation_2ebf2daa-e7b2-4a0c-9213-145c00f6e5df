package com.yy.gameecology.activity.service;

import com.yy.gameecology.activity.annotation.NeedRecycle;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.protocol.pb.GameecologyActivity;
import com.yy.thrift.broadcast.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
@Deprecated
public class DelaySvcSDKService {

    private static final String DELAY_BROADCAST_PREFIX = "DELAY_BROADCAST_";

    @Resource(name = "zpopByScoreScript")
    private DefaultRedisScript<List<GameecologyActivity.GameEcologyMsg>> zpopByScoreScript;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Autowired
    private ActRedisGroupDao actRedisDao;

    @Autowired
    private SvcSDKService svcSDKService;

    public static final RedisSerializer GAME_ECOLOGY_MSG_REDIS_SERIALIZER = new RedisSerializer<GameecologyActivity.GameEcologyMsg>() {
        @Override
        public byte[] serialize(GameecologyActivity.GameEcologyMsg gameEcologyMsg) throws SerializationException {
            return gameEcologyMsg.toByteArray();
        }

        @Override
        public GameecologyActivity.GameEcologyMsg deserialize(byte[] bytes) throws SerializationException {
            try {
                return GameecologyActivity.GameEcologyMsg.parseFrom(bytes);
            } catch (Exception e) {
                log.error("GAME_ECOLOGY_MSG_REDIS_SERIALIZER parse object exception:", e);
            }

            return null;
        }
    };

    /**
     * 函数功能：单播
     */
    public void unicastUid(long uid, GameecologyActivity.GameEcologyMsg message, long delay) {
        throw new UnsupportedOperationException("暂不支持");
    }

    /**
     * 函数功能：子频道广播
     */
    public void broadcastSub(long sid, long ssid, GameecologyActivity.GameEcologyMsg message, long delay) {
        throw new UnsupportedOperationException("暂不支持");
    }

    /**
     * 函数功能：顶级频道广播
     */
    public void broadcastTop(long sid, GameecologyActivity.GameEcologyMsg message, long delay) {
        throw new UnsupportedOperationException("暂不支持");
    }

    /**
     * 函数功能：模板全服广播
     */
    public void broadcastTemplate(Template template, GameecologyActivity.GameEcologyMsg message, long delay) {
        String key = DELAY_BROADCAST_PREFIX + SysEvHelper.getGroup() + "_" + template.name();
        final byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
        final byte[] datas = message.toByteArray();
        final long score = System.currentTimeMillis() + delay;
        actRedisDao.getRedisTemplate(RedisConfigManager.DEFAULT_GROUP_CODE).execute((RedisCallback<Object>) connection -> connection.zAdd(keyBytes, score, datas));
    }

    @Scheduled(cron = "1/2 * * * * ?")
    @NeedRecycle(author = "liqingyang", notRecycle = true)
    public void doBroadcastTemplate() {
        if (SysEvHelper.isLocal()) {
            log.info("local not run return");
            return;
        }
        for (Template template : Template.values()) {
            String key = DELAY_BROADCAST_PREFIX + SysEvHelper.getGroup() + "_" + template.name();
            String max = String.valueOf(System.currentTimeMillis());
            List<GameecologyActivity.GameEcologyMsg> result = actRedisDao.getRedisTemplate(RedisConfigManager.DEFAULT_GROUP_CODE)
                    .execute(zpopByScoreScript, RedisSerializer.string(), GAME_ECOLOGY_MSG_REDIS_SERIALIZER, Collections.singletonList(key), "0", max, "5");
            if (CollectionUtils.isEmpty(result)) {
                continue;
            }

            for (GameecologyActivity.GameEcologyMsg ecologyMsg : result) {
                if (SysEvHelper.isDev()) {
                    log.info("DelaySvcSDKService broadcastTemplate with:{}", ecologyMsg);
                }
                svcSDKService.broadcastTemplate(template, ecologyMsg);
            }
        }
    }

    /**
     * 函数功能：陪玩全频道广播
     */
    public void broadcastAllChanelsInPW(long actId, GameecologyActivity.GameEcologyMsg message, long delay) {
        throw new UnsupportedOperationException("暂不支持");
    }

    /**
     * 函数功能：技能卡全频道广播
     */
    public void broadcastAllChanelsInSkillCard(GameecologyActivity.GameEcologyMsg message, long delay) {
        throw new UnsupportedOperationException("暂不支持");
    }

    /**
     * 飞行任务广播
     *
     * @param template
     * @param message
     * @param sid
     * @param ssid
     */
    public void broadcastTemplate(Template template, GameecologyActivity.GameEcologyMsg message, long sid, long ssid, long delay) {
        throw new UnsupportedOperationException("暂不支持");
    }
}
