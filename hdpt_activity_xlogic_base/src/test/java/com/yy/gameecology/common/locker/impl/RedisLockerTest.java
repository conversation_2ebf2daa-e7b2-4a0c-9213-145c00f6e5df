package com.yy.gameecology.common.locker.impl;

import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.locker.Locker;
import com.yy.gameecology.common.locker.Secret;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR> 2019/9/3
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties"})
public class RedisLockerTest {

    @Autowired
    private Locker locker;
    @Autowired
    private ActRedisGroupDao actRedisDao;

    static {
        System.setProperty("group", "1");
        System.setProperty("history", "1");
    }

    @Test
    public void testLock() throws Exception {
        actRedisDao.getRedisTemplate(RedisConfigManager.OLD_ACT_GROUP_CODE).opsForValue().setIfPresent("texttest", "1");

        Secret lock = locker.lock("lock", 1000);
        locker.unlock("lock", lock);
    }

    @Test
    public void testZSetNX() throws Exception {
        String key = "test_z_set_nx";
        boolean result1 = actRedisDao.zSetNX(RedisConfigManager.OLD_ACT_GROUP_CODE, "test_z_set_nx", "aa", 3);
        boolean result2 = actRedisDao.zSetNX(RedisConfigManager.OLD_ACT_GROUP_CODE, "test_z_set_nx", "bb", 3);
        boolean result3 = actRedisDao.zSetNX(RedisConfigManager.OLD_ACT_GROUP_CODE, "test_z_set_nx", "cc", 3);
        boolean result4 = actRedisDao.zSetNX(RedisConfigManager.OLD_ACT_GROUP_CODE, "test_z_set_nx", "dd", 3);
        boolean result5 = actRedisDao.zSetNX(RedisConfigManager.OLD_ACT_GROUP_CODE, "test_z_set_nx", "ee", 3);
        Thread.sleep(3000);
        boolean result6 = actRedisDao.zSetNX(RedisConfigManager.OLD_ACT_GROUP_CODE, "test_z_set_nx", "aa", 3);
        boolean result7 = actRedisDao.zSetNX(RedisConfigManager.OLD_ACT_GROUP_CODE, "test_z_set_nx", "bb", 3);
        System.out.println();

    }

    @Test
    public void testPikalog() throws Exception {
        test();
        System.out.println();

    }

    public void test() {
        try {
            String name = "20190717_locker_test_by_guoliping";
            //Secret s1 = new Secret(new AtomicLong(1), 20, null);
            Secret s2 = new Secret(new AtomicLong(2), 30, "");
            String s3 = s2.toString();
            Secret s4 = Secret.toSecret(s3);
            System.out.println("s4 = " + s4);
            int ttl = 600;


            Secret secret0 = locker.replace(name, new Secret(), ttl);
            boolean b0 = locker.unlock(name, secret0);
            System.out.println("b0 = " + b0);
            Secret secret1 = locker.lock(name, ttl);

            String content = secret1.toString();
            Secret object = Secret.toSecret(content);
            System.out.println("object = " + object);

            Secret secret2 = locker.lock(name, ttl);
            Secret secret3 = locker.replace(name, secret1, ttl);
            boolean b1 = locker.unlock(name, secret1);
            boolean b2 = locker.unlock(name, secret2);
            boolean b3 = locker.unlock(name, secret3);
            Secret secret4 = locker.lock(name, ttl);
            boolean b4 = locker.expire(name, secret4, 3600);
            boolean b5 = locker.review(name, secret4, 3600);
            boolean b6 = locker.expire(name, new Secret(), 1000);
            boolean b7 = locker.expire(name, null, 1000);
            Secret secret5 = locker.replace(name, new Secret(), 1000);
            Secret secret6 = locker.replace(name, null, 1000);
            boolean b8 = locker.unlock(name, secret4);
            boolean b9 = locker.expire(name, secret4, 3000);
            Secret secret7 = locker.replace(name, secret4, 1000);
            System.out.println("b6 = " + b6);
            System.out.println("b7 = " + b7);
            System.out.println("secret5 = " + secret5);
            System.out.println("secret6 = " + secret6);
            System.out.println("b8 = " + b8);
            System.out.println("b9 = " + b9);
            System.out.println("secret7 = " + secret7);

            System.out.println("secret1 = " + secret1);
            System.out.println("secret2 = " + secret2);
            System.out.println("secret3 = " + secret3);
            System.out.println("b1 = " + b1);
            System.out.println("b2 = " + b2);
            System.out.println("b3 = " + b3);
            System.out.println("secret4 = " + secret4);
            System.out.println("b4 = " + b4);
            System.out.println("b5 = " + b5);
        } catch (Throwable t) {
            t.printStackTrace();
        }
    }


}
