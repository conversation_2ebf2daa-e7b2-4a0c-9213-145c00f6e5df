package com.yy.gameecology.dao;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yy.gameecology.activity.Main;
import com.yy.gameecology.activity.config.redis.RedisConfigManager;
import com.yy.gameecology.activity.dao.redis.ActRedisGroupDao;
import com.yy.gameecology.common.bean.HashIncParameter;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.StringUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2021-07-23 16:07
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class)
@TestPropertySource(value = {"classpath:env/local/application.properties", "classpath:env/local/group-setting-1.properties", "classpath:env/local/application-inner.properties"})
public class ActRedisGroupDaoTest {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    static {
        System.setProperty("group", "1");
    }

    @Autowired
    private ActRedisGroupDao actRedisGroupDao;

    @Autowired
    private RedisConfigManager redisConfigManager;

    @Test
    public void redisTest() {
        //默认公共key
        actRedisGroupDao.set(redisConfigManager.DEFAULT_GROUP_CODE, "redis_group_test1", "这里是第一套环境");

        //这里是老活动，应该按照老的来，
        actRedisGroupDao.set(redisConfigManager.getGroupCode(1L), "redis_group_test2", "这里是第一套环境13库");

        //这里是特别配置的活动，按照配置来，配置到了第四套redis分组
        actRedisGroupDao.set(redisConfigManager.getGroupCode(2021032002L), "redis_group_test3", "这里是第四套redis分组,测试环境db4");
    }

    @Test
    public void hbatchSetTest() {
        List<HashIncParameter> parameters = Lists.newArrayList();
        HashIncParameter inc1 = new HashIncParameter();
        inc1.setKey("htest1");
        inc1.setField("zengwenzhi");
        inc1.setInc("1");
        inc1.setLimit("1");
        inc1.setLimitType("1");
        parameters.add(inc1);

        HashIncParameter inc2 = new HashIncParameter();
        inc2.setKey("htest1");
        inc2.setField("zengwenzhi2");
        inc2.setInc("-1");
        inc2.setLimit("-1");
        inc2.setLimitType("-1");
        parameters.add(inc2);

        actRedisGroupDao.hBatchIncrWithLimit("2", parameters);
    }

    @Test
    public void hsetWithSeqTest() {
        actRedisGroupDao.hIncrByKeyWithSeq("3", "123456", "hsettest", "test", 1, 0);
    }

    @Test
    public void lineUpCountdown() {
        doLineUpCountdown();
    }

    private void doLineUpCountdown() {
        Clock clock = new Clock();
        String groupCode = "3";
        String listKey = "asuperwinner:lineUpCountdown:list";
        String boxItem = actRedisGroupDao.lindex(groupCode, listKey, 0);

        if (boxItem == null) {
            // log.info("processBox ignore@listKey:{} is empty! {}", listKey, clock.tag());
            return;
        }

        // 2. 去掉无效的宝箱条目
        if (!isValidBoxItem(boxItem)) {
            long size = actRedisGroupDao.lrem(groupCode, listKey, boxItem);
            if (size < 1) {
                log.error("processBox invalid1@boxItem:{}, listKey:{} modified by other!!! {}", boxItem, listKey, clock.tag());
            } else {
                log.error("processBox invalid2@boxItem:{}, listKey:{} removed size:{} {}", boxItem, listKey, size, clock.tag());
            }
            return;
        }

        long boxShowSeconds = 3600;
        Date now = new Date();
        String[] values = boxItem.split("\\|");
        long startTime = Long.parseLong(values[0]);
        String boxId = values[1];
        long left = getLeft(now, startTime, boxShowSeconds);
        String boxKey = "asuperwinner:lineUpCountdown:box:" + boxId;
        long nowTime = Long.parseLong(DateUtil.format(now, DateUtil.PATTERN_TYPE1));

        long ret = actRedisGroupDao.lineUpCountdown(groupCode, listKey, boxKey, left, boxId, startTime, nowTime, boxShowSeconds);
        System.out.println("ret = " + ret);
    }

    private boolean isValidBoxItem(String boxItem) {
        if (StringUtil.isBlank(boxItem)) {
            return false;
        }
        String[] values = boxItem.split("\\|");
        if (values.length != 2) {
            return false;
        }
        // 首元素为 0 或者是 yyyyMMddHHmmss 格式的字符串
        String v = values[0];
        return v.equals("0") ? true : DateUtil.getDate(v, DateUtil.PATTERN_TYPE1) != null;
    }

    private long getLeft(Date now, long startTime, long boxShowSeconds) {
        if (startTime == 0) {
            return boxShowSeconds;
        }
        Date start = DateUtil.getDate(String.valueOf(startTime), DateUtil.PATTERN_TYPE1);
        long sNow = DateUtil.getSeconds(now);
        long sOld = DateUtil.getSeconds(start);
        return boxShowSeconds - (sNow - sOld);
    }

    @Test
    public void lpushWithSeqTest() {
        actRedisGroupDao.lPushWithSeq("1", "lpush_test:seq_20220818", "lpush_test", "lpushTestValue", 60 * 5);
    }

    @Test
    public void incValueWithSeqTest() {
        actRedisGroupDao.incrValueWithLimitSeq("1", "inc_val_with_seq_20220818", "inc_val_test", 1, 100, true, 60 * 5);
    }


    @Test
    public void hincWithSeqTest() {
        actRedisGroupDao.hIncrByKeyWithSeq("1", "hinc_val_with_seq_20220818001", "hinc_val_test", "test", 1, 60 * 5);

    }

    @Test
    public void hCompareAndDel() {
        for(int i=0; i< 100; i++) {
            try {
                boolean flag = actRedisGroupDao.hCompareAndDel("3", "glp_test_001", "hello", "guoliping");
                System.out.print(flag);
            }catch (Throwable t) {
                t.printStackTrace();
            }
        }
    }

    @Test
    public void hincTest(){
        String redisGroup = redisConfigManager.getGroupCode(2023112001L);
        List<Long>  result1 = actRedisGroupDao.hIncrWithLimitSeq(redisGroup,"zwztesthtest_seq_001","zwztesthtest","1",1,10,false,1008600);
        System.out.println("hincTest==============="+ JSON.toJSON(result1));

        //seq相同，hinc失败
        result1 = actRedisGroupDao.hIncrWithLimitSeq(redisGroup,"zwztesthtest_seq_001","zwztesthtest","1",1,10,false,1008600);
        System.out.println("hincTest==============="+ JSON.toJSON(result1));

        //超出限制，hic失败
        result1 = actRedisGroupDao.hIncrWithLimitSeq(redisGroup,"zwztesthtest_seq_002","zwztesthtest","1",10,10,false,1008600);
        System.out.println("hincTest==============="+ JSON.toJSON(result1));

        //部分增加，只能增加到10
        result1 = actRedisGroupDao.hIncrWithLimitSeq(redisGroup,"zwztesthtest_seq_003","zwztesthtest","1",10,10,true,1008600);
        System.out.println("hincTest==============="+ JSON.toJSON(result1));

        //部分增加，只能增加到10
        result1 = actRedisGroupDao.hIncrWithLimitSeq(redisGroup,"zwztesthtest_seq_004","zwztesthtest","1",10,10,true,1008600);
        System.out.println("hincTest==============="+ JSON.toJSON(result1));

        //seq重复，返回上次结果
        result1 = actRedisGroupDao.hIncrWithLimitSeq(redisGroup,"zwztesthtest_seq_003","zwztesthtest","1",10,100,true,1008600);
        System.out.println("hincTest==============="+ JSON.toJSON(result1));
    }

    private static final String POP_SCRIPT = """
            local result = redis.call('ZRANGEBYSCORE', 'this_is_a_test_zset', 0, 10000000000000)
            return result
            """;

    private static final RedisScript<List> SCRIPT = new DefaultRedisScript<>(POP_SCRIPT, List.class);

    private static final RedisSerializer SERIALIZER = RedisSerializer.json();

    @Test
    public void test() {
        StringRedisTemplate redisTemplate = actRedisGroupDao.getRedisTemplate("1");
        String myKey = "this_is_a_test_zset";
        byte[] data = SERIALIZER.serialize(new Date());

        if (data == null) {
            return;
        }
        redisTemplate.execute((RedisCallback<Object>) connection -> connection.zAdd(myKey.getBytes(StandardCharsets.UTF_8), System.currentTimeMillis(), data));


        System.out.println(redisTemplate.opsForZSet().rangeByScore(myKey, 0, 10000000000000L));
        Object rs = redisTemplate.execute(SCRIPT, RedisSerializer.string(), SERIALIZER, List.of(myKey), "0", "10000000000000");
        System.out.println(rs);
    }


}
